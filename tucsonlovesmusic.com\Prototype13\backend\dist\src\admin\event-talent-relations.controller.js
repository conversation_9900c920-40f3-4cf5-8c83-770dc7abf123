"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventTalentRelationsController = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const auth_guard_1 = require("../auth/auth.guard");
const sql = __importStar(require("mssql"));
const dotenv = __importStar(require("dotenv"));
const talent_entity_1 = require("../talent/talent.entity");
const events_entity_1 = require("../events/events.entity");
const event_talent_entity_1 = require("../events/event-talent.entity");
const swagger_1 = require("@nestjs/swagger");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class UpdateEventTalentsDto {
}
let EventTalentRelationsController = class EventTalentRelationsController {
    extractSocialLinksFromDescription(description) {
        const links = {
            website: undefined,
            facebook: undefined,
            twitter: undefined,
            instagram: undefined,
            youtube: undefined,
            spotify: undefined
        };
        if (!description)
            return links;
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        const urls = description.match(urlRegex) || [];
        for (const url of urls) {
            const lowerUrl = url.toLowerCase();
            if (lowerUrl.includes('facebook.com'))
                links.facebook = url;
            else if (lowerUrl.includes('twitter.com') || lowerUrl.includes('x.com'))
                links.twitter = url;
            else if (lowerUrl.includes('instagram.com'))
                links.instagram = url;
            else if (lowerUrl.includes('youtube.com'))
                links.youtube = url;
            else if (lowerUrl.includes('spotify.com'))
                links.spotify = url;
            else if (!links.website)
                links.website = url;
        }
        return links;
    }
    constructor(dataSource) {
        this.dataSource = dataSource;
        this.azurePoolPromise = null;
        this.connectionRetryAttempts = 3;
        this.connectionRetryDelay = 3000;
        this.lastConnectionFailure = null;
        this.circuitBreakerWindow = 60000;
        dotenv.config();
        this.azureConfig = {
            server: process.env.AZURE_DB_SERVER || 'mssql.drv1.umbhost.net',
            database: process.env.AZURE_DB_NAME || 'TLM',
            user: process.env.AZURE_DB_USER || 'Reader',
            password: process.env.AZURE_DB_PASSWORD || 'TLM1234!',
            options: {
                encrypt: false,
                trustServerCertificate: true,
                enableArithAbort: true,
                connectTimeout: 30000,
                requestTimeout: 30000
            },
            pool: {
                max: 10,
                min: 1,
                idleTimeoutMillis: 30000,
                acquireTimeoutMillis: 15000
            }
        };
    }
    async getAzurePool() {
        console.log('🔗 Creating Azure SQL connection with retry logic...');
        try {
            if (!this.azurePoolPromise) {
                return await this.createNewPool();
            }
            const pool = await this.azurePoolPromise;
            if (pool && pool.connected) {
                await pool.request().query('SELECT 1 AS test');
                console.log('✅ Reusing existing Azure SQL connection');
                return pool;
            }
            else {
                console.log('🔄 Existing pool not connected, creating new one...');
                await this.closeExistingPool();
                return await this.createNewPool();
            }
        }
        catch (error) {
            console.warn('🔄 Connection test failed, creating new pool:', error instanceof Error ? error.message : 'Unknown error');
            await this.closeExistingPool();
            return await this.createNewPool();
        }
    }
    async closeExistingPool() {
        if (this.azurePoolPromise) {
            try {
                const pool = await this.azurePoolPromise;
                if (pool && pool.connected) {
                    await pool.close();
                }
            }
            catch (error) {
                console.warn('Error closing Azure SQL pool:', error instanceof Error ? error.message : 'Unknown error');
            }
            finally {
                this.azurePoolPromise = null;
            }
        }
    }
    async createNewPool() {
        if (this.lastConnectionFailure) {
            const timeSinceFailure = Date.now() - this.lastConnectionFailure.getTime();
            if (timeSinceFailure < this.circuitBreakerWindow) {
                throw new Error(`Circuit breaker open: Azure connection failed recently, waiting ${Math.ceil((this.circuitBreakerWindow - timeSinceFailure) / 1000)}s before retry`);
            }
        }
        let retryCount = 0;
        let lastError = null;
        while (retryCount < this.connectionRetryAttempts) {
            try {
                this.azurePoolPromise = new sql.ConnectionPool(this.azureConfig).connect();
                const pool = await this.azurePoolPromise;
                await pool.request().query('SELECT 1 AS testConnection');
                console.log(`✅ Successfully connected to Azure SQL (attempt ${retryCount + 1})`);
                this.lastConnectionFailure = null;
                return pool;
            }
            catch (error) {
                lastError = error;
                retryCount++;
                console.warn(`Azure SQL connection attempt ${retryCount} failed:`, error instanceof Error ? error.message : 'Unknown error');
                this.azurePoolPromise = null;
                if (retryCount < this.connectionRetryAttempts) {
                    await new Promise(resolve => setTimeout(resolve, this.connectionRetryDelay));
                }
            }
        }
        console.error('❌ All Azure SQL connection attempts failed, activating circuit breaker');
        this.lastConnectionFailure = new Date();
        throw lastError;
    }
    async getEventTalentStatusCounts(year) {
        return this.getEventTalentStatusCountsInternal(year);
    }
    async getEventTalentStatusCountsStatus() {
        return this.getEventTalentStatusCountsInternal();
    }
    async getEventTalentStatusCountsInternal(year) {
        let azurePool = null;
        try {
            azurePool = await this.getAzurePool();
            const yearFilter = year ? `AND EXTRACT(YEAR FROM "startDateTime"::timestamp) >= ${parseInt(year)}` : '';
            const totalEventsResult = await this.dataSource.query(`
        SELECT COUNT(*) as total 
        FROM event 
        WHERE deleted = false ${yearFilter}
      `);
            const totalEvents = parseInt(totalEventsResult[0].total);
            const missingTalentsResult = await this.dataSource.query(`
        SELECT COUNT(DISTINCT e.id) as count 
        FROM event e
        LEFT JOIN event_talents et ON e.id = et.event_id
        WHERE e.deleted = false
        AND e.azure_id IS NOT NULL
        AND et.talent_id IS NULL
        ${yearFilter}
      `);
            const missingTalents = parseInt(missingTalentsResult[0].count);
            const mismatchedResult = await this.dataSource.query(`
        WITH event_talent_counts AS (
          SELECT 
            e.id,
            e.azure_id,
            COUNT(et.talent_id) as talent_count
          FROM event e
          LEFT JOIN event_talents et ON e.id = et.event_id
          WHERE e.deleted = false AND e.azure_id IS NOT NULL ${yearFilter}
          GROUP BY e.id, e.azure_id
        )
        SELECT COUNT(*) as count
        FROM event_talent_counts etc
        WHERE etc.talent_count > 0
      `);
            const mismatchedTalents = parseInt(mismatchedResult[0].count);
            const potentialFixesResult = await this.dataSource.query(`
        SELECT COUNT(*) as count 
        FROM event e
        WHERE e.deleted = false
        AND e.azure_id IS NOT NULL
        ${yearFilter}
      `);
            const potentialFixes = parseInt(potentialFixesResult[0].count);
            return {
                total: totalEvents,
                missingTalents,
                mismatchedTalents,
                potentialFixes
            };
        }
        catch (error) {
            console.error('Error in getEventTalentStatusCountsInternal:', error instanceof Error ? error.message : 'Unknown error');
            if (error instanceof Error) {
                const errorMessage = error.message.toLowerCase();
                if (errorMessage.includes('connection is closed') ||
                    errorMessage.includes('etimeout') ||
                    errorMessage.includes('econnclosed') ||
                    errorMessage.includes('failed to connect')) {
                    console.warn('🔄 Azure connection issue detected in getEventTalentStatusCountsInternal, resetting pool for retry...');
                    await this.closeExistingPool();
                }
            }
            throw error;
        }
    }
    async getEventsWithMissingTalents(year) {
        try {
            console.log(`🔍 [STEP 1] Starting missing talents query with year: ${year}`);
            console.log(`🔍 [STEP 2] Testing Azure connection...`);
            let azurePool;
            try {
                azurePool = await this.getAzurePool();
                console.log(`🔍 [STEP 2] ✅ Azure connection successful`);
            }
            catch (azureError) {
                console.error(`🔍 [STEP 2] ❌ Azure connection failed:`, azureError);
                throw new Error(`Azure connection failed: ${azureError.message}`);
            }
            console.log(`🔍 [STEP 3] Testing simple Azure query...`);
            try {
                const testResult = await azurePool.request().query('SELECT COUNT(*) as count FROM PerformanceEventModelTalentModel');
                console.log(`🔍 [STEP 3] ✅ Azure test query successful. Total relationships: ${testResult.recordset[0].count}`);
            }
            catch (azureQueryError) {
                console.error(`🔍 [STEP 3] ❌ Azure test query failed:`, azureQueryError);
                throw new Error(`Azure query failed: ${azureQueryError.message}`);
            }
            console.log(`🔍 [STEP 4] Testing Terry Fox specific query...`);
            try {
                const terryResult = await azurePool.request()
                    .input('eventId', sql.UniqueIdentifier, 'FB2DDAD5-5EA3-4542-8CC2-5988CB58C464')
                    .query(`
            SELECT t.Id as talentId, t.Name as talentName
            FROM PerformanceEventModelTalentModel pemtm
            JOIN Talent t ON pemtm.TalentListId = t.Id
            WHERE pemtm.PerformanceEventsId = @eventId
          `);
                console.log(`🔍 [STEP 4] ✅ Terry Fox Azure query successful. Found ${terryResult.recordset.length} talents:`, terryResult.recordset);
            }
            catch (terryError) {
                console.error(`🔍 [STEP 4] ❌ Terry Fox Azure query failed:`, terryError);
            }
            const yearFilter = year ? `AND EXTRACT(YEAR FROM e."startDateTime"::timestamp) >= ${parseInt(year)}` : '';
            console.log(`🔍 [STEP 5] Year filter: ${yearFilter}`);
            console.log(`🔍 [STEP 6] Testing PostgreSQL query...`);
            const query = `
        SELECT 
          e.id, 
          e.name, 
          e.azure_id,
          e."startDateTime" as date,
          v.name as venue_name,
          COUNT(et.talent_id) as postgres_talent_count
        FROM event e
        LEFT JOIN venue v ON e.venue_id = v.id
        LEFT JOIN event_talents et ON e.id = et.event_id
        WHERE e.deleted = false
        AND e.azure_id IS NOT NULL
        ${yearFilter}
        GROUP BY e.id, e.name, e."startDateTime", v.name, e.azure_id
        HAVING COUNT(et.talent_id) = 0
        ORDER BY e."startDateTime" ASC
        LIMIT 10
      `;
            const events = await this.dataSource.query(query);
            console.log(`🔍 [STEP 6] ✅ PostgreSQL query successful. Found ${events.length} events with 0 talent relationships`);
            events.slice(0, 3).forEach((event, index) => {
                console.log(`🔍 [STEP 6] Event ${index + 1}: ${event.name} (Azure ID: ${event.azure_id})`);
            });
            const results = events.slice(0, 3).map(event => ({
                id: event.id,
                name: event.name,
                date: event.date,
                venue_name: event.venue_name,
                azure_talent_ids: ['DEBUG-PLACEHOLDER'],
                postgres_talent_count: 0
            }));
            console.log(`🔍 [STEP 7] Returning ${results.length} events for debugging`);
            if (azurePool) {
                await azurePool.close();
            }
            return results;
        }
        catch (error) {
            console.error('🔍 [ERROR] Error in getEventsWithMissingTalents:', error);
            throw error;
        }
    }
    async getEventsWithMismatchedTalents(year) {
        let azurePool = null;
        const results = [];
        try {
            azurePool = await this.getAzurePool();
            const yearFilter = year ? `AND EXTRACT(YEAR FROM e."startDateTime"::timestamp) >= ${parseInt(year)}` : '';
            const query = `
        SELECT 
          e.id, 
          e.name, 
          e.azure_id,
          e."startDateTime" as date,
          v.name as venue_name,
          COUNT(et.talent_id) as postgres_talent_count
        FROM event e
        LEFT JOIN venue v ON e.venue_id = v.id
        LEFT JOIN event_talents et ON e.id = et.event_id
        WHERE e.deleted = false
        AND e.azure_id IS NOT NULL
        ${yearFilter}
        GROUP BY e.id, e.name, e."startDateTime", v.name, e.azure_id
        HAVING COUNT(et.talent_id) > 0
        ORDER BY e."startDateTime" DESC
        LIMIT 100
      `;
            const events = await this.dataSource.query(query);
            for (const event of events) {
                try {
                    if (!azurePool || !azurePool.connected) {
                        console.log(`🔄 Reconnecting to Azure SQL for event: ${event.name}`);
                        azurePool = await this.getAzurePool();
                    }
                    const azureResult = await azurePool.request()
                        .input('eventId', sql.UniqueIdentifier, event.azure_id)
                        .query(`
              SELECT COUNT(*) as talentCount
              FROM PerformanceEventModelTalentModel pemtm
              WHERE pemtm.PerformanceEventsId = @eventId
            `);
                    const azureTalentCount = azureResult.recordset[0].talentCount;
                    const postgresTalentCount = parseInt(event.postgres_talent_count);
                    const azureTalentsResult = await azurePool.request()
                        .input('eventId', sql.UniqueIdentifier, event.azure_id)
                        .query(`
              SELECT t.Id as talentId, t.Name as talentName
              FROM PerformanceEventModelTalentModel pemtm
              JOIN Talent t ON pemtm.TalentListId = t.Id
              WHERE pemtm.PerformanceEventsId = @eventId
            `);
                    const azureTalentIds = azureTalentsResult.recordset.map(talent => talent.talentId);
                    if (azureTalentCount !== postgresTalentCount) {
                        results.push({
                            id: event.id,
                            name: event.name,
                            date: event.date,
                            venue_name: event.venue_name,
                            azure_talent_ids: azureTalentIds,
                            postgres_talent_count: postgresTalentCount
                        });
                    }
                }
                catch (eventError) {
                    console.warn(`⚠️ Failed to process event "${event.name}" (${event.azure_id}):`, eventError instanceof Error ? eventError.message : 'Unknown error');
                    if (eventError instanceof Error) {
                        const errorMessage = eventError.message.toLowerCase();
                        if (errorMessage.includes('connection is closed') ||
                            errorMessage.includes('etimeout') ||
                            errorMessage.includes('econnclosed') ||
                            errorMessage.includes('failed to connect')) {
                            console.log(`🔄 Connection issue for event "${event.name}", resetting pool...`);
                            await this.closeExistingPool();
                            azurePool = null;
                        }
                    }
                    continue;
                }
            }
            console.log(`✅ Successfully processed ${events.length} events for mismatched talent check, found ${results.length} with mismatched counts`);
            return results;
        }
        catch (error) {
            console.error('Error in getEventsWithMismatchedTalents:', error instanceof Error ? error.message : 'Unknown error');
            if (error instanceof Error) {
                const errorMessage = error.message.toLowerCase();
                if (errorMessage.includes('connection is closed') ||
                    errorMessage.includes('etimeout') ||
                    errorMessage.includes('econnclosed') ||
                    errorMessage.includes('failed to connect')) {
                    console.warn('🔄 Azure connection issue detected in getEventsWithMismatchedTalents, resetting pool for retry...');
                    await this.closeExistingPool();
                }
            }
            console.log(`⚠️ Returning ${results.length} events that were successfully processed before error`);
            return results;
        }
    }
    async fixEventTalentRelationship(updateDto) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const { eventId, talentIds } = updateDto;
            await queryRunner.query(`
        DELETE FROM event_talents 
        WHERE event_id = $1
      `, [eventId]);
            for (const talentId of talentIds) {
                await queryRunner.query(`
          INSERT INTO event_talents (event_id, talent_id)
          VALUES ($1, $2)
        `, [eventId, talentId]);
            }
            await queryRunner.commitTransaction();
            return { success: true, message: 'Event-talent relationships updated successfully' };
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async fixSingleEventTalentRelationship(eventId) {
        console.log(`🔧 [Backend] Starting fix for event ID: ${eventId}`);
        let azurePool = null;
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        console.log(`🔧 [Backend] PostgreSQL transaction started`);
        try {
            azurePool = await this.getAzurePool();
            const stats = {
                relationshipsAdded: 0,
                relationshipsRemoved: 0,
                talentsNotFound: 0
            };
            const pgEvent = await queryRunner.query(`
        SELECT id, azure_id, name
        FROM event
        WHERE id = $1 AND deleted = false
      `, [eventId]);
            if (!pgEvent || pgEvent.length === 0 || !pgEvent[0].azure_id) {
                throw new Error(`Event not found or missing Azure ID: ${eventId}`);
            }
            const event = pgEvent[0];
            const pgTalents = await queryRunner.query(`
        SELECT id, azure_id, name
        FROM talent
        WHERE deleted = false AND azure_id IS NOT NULL
      `);
            const talentAzureToPostgresMap = new Map();
            pgTalents.forEach(talent => {
                if (talent.azure_id) {
                    talentAzureToPostgresMap.set(talent.azure_id.toLowerCase(), talent.id);
                }
            });
            const currentRelationships = await queryRunner.query(`
        SELECT talent_id FROM event_talents WHERE event_id = $1
      `, [event.id]);
            const currentEventTalentIds = new Set(currentRelationships.map(rel => rel.talent_id));
            const azureResult = await azurePool.request()
                .input('eventId', sql.UniqueIdentifier, event.azure_id)
                .query(`
          SELECT t.Id as talentId
          FROM PerformanceEventModelTalentModel pemtm
          JOIN Talent t ON pemtm.TalentListId = t.Id
          WHERE pemtm.PerformanceEventsId = @eventId
        `);
            const azureTalentIds = azureResult.recordset.map(t => t.talentId.toLowerCase());
            const matchedTalentIds = [];
            for (const azureTalentId of azureTalentIds) {
                let pgTalentId = talentAzureToPostgresMap.get(azureTalentId);
                if (pgTalentId) {
                    console.log(`🔧 [Backend] Found existing talent in PostgreSQL with ID: ${pgTalentId} for Azure ID: ${azureTalentId}`);
                    matchedTalentIds.push(pgTalentId);
                }
                else {
                    console.log(`🔧 [Backend] No existing talent found for Azure ID: ${azureTalentId}, attempting to import from Azure...`);
                    try {
                        const talentResult = await azurePool.request()
                            .input('talentId', sql.UniqueIdentifier, azureTalentId)
                            .query(`
                SELECT 
                  t.Id,
                  t.Name,
                  t.Description,
                  t.ContactName,
                  t.ContactEmailAddress,
                  t.ContactPhoneNumber,
                  t.ContactWebsite,
                  t.Url,
                  t.CreatedOn,
                  t.UpdatedOn,
                  t.ProfileImageCropperValue as cropData,
                  t.FeatureLevel
                FROM Talent t
                WHERE t.Id = @talentId
              `);
                        if (talentResult.recordset.length === 0) {
                            console.log(`🔧 [Backend] No talent found in Azure with ID: ${azureTalentId}`);
                            stats.talentsNotFound++;
                            continue;
                        }
                        const azureTalent = talentResult.recordset[0];
                        console.log(`🔧 [Backend] Found talent in Azure: ${azureTalent.Name}, importing to PostgreSQL...`);
                        let genre = [];
                        try {
                            try {
                                const genreResult = await azurePool.request()
                                    .input('talentId', sql.UniqueIdentifier, azureTalentId)
                                    .query(`
                    SELECT eg.Name as GenreName
                    FROM EntertainmentGenreModelTalentModel egtml
                    JOIN EntertainmentGenres eg ON egtml.GenresId = eg.Id
                    WHERE egtml.TalentsId = @talentId
                  `);
                                if (genreResult.recordset.length > 0) {
                                    genre = genreResult.recordset.map(g => g.GenreName);
                                    console.log(`🔧 [Backend] Found ${genre.length} genres for talent: ${genre.join(', ')}`);
                                }
                                else {
                                    console.log(`🔧 [Backend] No genres found in EntertainmentGenreModelTalentModel table`);
                                }
                            }
                            catch (entGenreError) {
                                console.log(`🔧 [Backend] Failed to query EntertainmentGenreModelTalentModel, trying fallback...`);
                                try {
                                    const hasGenreIdResult = await azurePool.request()
                                        .query(`
                      SELECT TOP 1 1
                      FROM INFORMATION_SCHEMA.COLUMNS
                      WHERE TABLE_NAME = 'Talent' AND COLUMN_NAME = 'GenreId'
                    `);
                                    if (hasGenreIdResult.recordset.length > 0) {
                                        const directGenreResult = await azurePool.request()
                                            .input('talentId', sql.UniqueIdentifier, azureTalentId)
                                            .query(`
                        SELECT g.Name as GenreName
                        FROM Talent t
                        JOIN EntertainmentGenres g ON t.GenreId = g.Id
                        WHERE t.Id = @talentId AND t.GenreId IS NOT NULL
                      `);
                                        if (directGenreResult.recordset.length > 0) {
                                            genre = directGenreResult.recordset.map(g => g.GenreName);
                                            console.log(`🔧 [Backend] Found genre from Talent.GenreId: ${genre.join(', ')}`);
                                        }
                                    }
                                }
                                catch (directGenreError) {
                                    console.log(`🔧 [Backend] Failed to query direct genre relationship`);
                                }
                                if (azureTalent.Category && genre.length === 0) {
                                    genre = [azureTalent.Category];
                                    console.log(`🔧 [Backend] Using Category as genre fallback: ${azureTalent.Category}`);
                                }
                            }
                        }
                        catch (genreError) {
                            console.error(`🔧 [Backend] Failed to fetch genres for talent:`, genreError);
                        }
                        const socialLinks = this.extractSocialLinksFromDescription(azureTalent.Description || '');
                        let slug = azureTalent.Name
                            ? azureTalent.Name.toLowerCase()
                                .replace(/[^a-z0-9]+/g, '-')
                                .replace(/^-+|-+$/g, '')
                            : 'talent-' + Date.now();
                        const existingTalentBySlug = await queryRunner.query(`
              SELECT id, name, azure_id FROM talent WHERE slug = $1 AND deleted = false
            `, [slug]);
                        if (existingTalentBySlug.length > 0) {
                            const existingTalent = existingTalentBySlug[0];
                            console.log(`🔧 [Backend] Found existing talent with slug '${slug}': ${existingTalent.name} (ID: ${existingTalent.id}, Azure ID: ${existingTalent.azure_id})`);
                            if (existingTalent.azure_id && existingTalent.azure_id.toLowerCase() === azureTalentId.toLowerCase()) {
                                console.log(`🔧 [Backend] Existing talent matches Azure ID, using existing talent ID: ${existingTalent.id}`);
                                pgTalentId = existingTalent.id;
                                talentAzureToPostgresMap.set(azureTalentId, pgTalentId);
                                matchedTalentIds.push(pgTalentId);
                                continue;
                            }
                            else {
                                let counter = 1;
                                let uniqueSlug = `${slug}-${counter}`;
                                while (true) {
                                    const slugCheck = await queryRunner.query(`
                    SELECT id FROM talent WHERE slug = $1 AND deleted = false
                  `, [uniqueSlug]);
                                    if (slugCheck.length === 0) {
                                        slug = uniqueSlug;
                                        console.log(`🔧 [Backend] Generated unique slug: ${slug}`);
                                        break;
                                    }
                                    counter++;
                                    uniqueSlug = `${slug}-${counter}`;
                                }
                            }
                        }
                        const socialMediaJson = JSON.stringify({
                            facebook: socialLinks.facebook || null,
                            twitter: socialLinks.twitter || null,
                            instagram: socialLinks.instagram || null,
                            youtube: socialLinks.youtube || null,
                            spotify: socialLinks.spotify || null
                        });
                        const result = await queryRunner.query(`
              INSERT INTO talent (
                name, slug, bio, email, "phoneNumber", website, 
                "socialMedia", genre, azure_id, "createdAt", "updatedAt", deleted,
                "cropData", featured, videos
              ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
              )
              RETURNING id
            `, [
                            azureTalent.Name,
                            slug,
                            azureTalent.Description,
                            azureTalent.ContactEmailAddress,
                            azureTalent.ContactPhoneNumber,
                            azureTalent.ContactWebsite,
                            socialMediaJson,
                            `{${genre.map(g => `"${g}"`).join(',')}}`,
                            azureTalentId,
                            new Date(),
                            new Date(),
                            false,
                            azureTalent.cropData ? JSON.stringify(azureTalent.cropData) : null,
                            false,
                            '{}'
                        ]);
                        pgTalentId = result[0].id;
                        console.log(`🔧 [Backend] Successfully imported talent with ID: ${pgTalentId}`);
                        matchedTalentIds.push(pgTalentId);
                        talentAzureToPostgresMap.set(azureTalentId, pgTalentId);
                    }
                    catch (importError) {
                        console.error(`🔧 [Backend] Failed to import talent from Azure:`, importError);
                        stats.talentsNotFound++;
                    }
                }
            }
            const talentsToAdd = matchedTalentIds.filter(id => !currentEventTalentIds.has(id));
            const talentsToRemove = Array.from(currentEventTalentIds)
                .filter(id => !matchedTalentIds.includes(id));
            if (talentsToAdd.length > 0) {
                for (const talentId of talentsToAdd) {
                    await queryRunner.query(`
            INSERT INTO event_talents (event_id, talent_id)
            VALUES ($1, $2)
            ON CONFLICT DO NOTHING
          `, [event.id, talentId]);
                    stats.relationshipsAdded++;
                }
            }
            if (talentsToRemove.length > 0) {
                for (const talentId of talentsToRemove) {
                    await queryRunner.query(`
            DELETE FROM event_talents 
            WHERE event_id = $1 AND talent_id = $2
          `, [event.id, talentId]);
                    stats.relationshipsRemoved++;
                }
            }
            await queryRunner.commitTransaction();
            return {
                success: true,
                message: `Fixed event "${event.name}": added ${stats.relationshipsAdded} talent relationships, removed ${stats.relationshipsRemoved} talent relationships, ${stats.talentsNotFound} talents not found`
            };
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async fixAllEventTalentRelationshipsAggregated(year) {
        console.log(`🔧 [Backend] Starting aggregated batch fix for all event-talent relationships with year filter: ${year || 'none'}`);
        try {
            const missingEvents = await this.getEventsWithMissingTalents(year);
            const mismatchedEvents = await this.getEventsWithMismatchedTalents(year);
            const allProblematicEvents = new Map();
            missingEvents.forEach(event => {
                allProblematicEvents.set(event.id, event);
            });
            mismatchedEvents.forEach(event => {
                allProblematicEvents.set(event.id, event);
            });
            const eventsToFix = Array.from(allProblematicEvents.values());
            console.log(`🔧 [Backend] Found ${eventsToFix.length} events that need fixing (year filter: ${year || 'none'})`);
            if (eventsToFix.length === 0) {
                return {
                    success: true,
                    message: 'No events found that need fixing',
                    stats: {
                        totalProcessed: 0,
                        successfulFixes: 0,
                        failedFixes: 0,
                        relationshipsAdded: 0,
                        relationshipsRemoved: 0,
                        talentsNotFound: 0
                    }
                };
            }
            const overallStats = {
                totalProcessed: 0,
                successfulFixes: 0,
                failedFixes: 0,
                relationshipsAdded: 0,
                relationshipsRemoved: 0,
                talentsNotFound: 0
            };
            for (const event of eventsToFix) {
                try {
                    console.log(`🔧 [Backend] Processing event ${overallStats.totalProcessed + 1}/${eventsToFix.length}: ${event.name} (ID: ${event.id})`);
                    const result = await this.fixSingleEventTalentRelationship(event.id.toString());
                    if (result.success) {
                        overallStats.successfulFixes++;
                        const message = result.message;
                        const addedMatch = message.match(/added (\d+) talent relationships/);
                        const removedMatch = message.match(/removed (\d+) talent relationships/);
                        const notFoundMatch = message.match(/(\d+) talents not found/);
                        if (addedMatch) {
                            overallStats.relationshipsAdded += parseInt(addedMatch[1]);
                        }
                        if (removedMatch) {
                            overallStats.relationshipsRemoved += parseInt(removedMatch[1]);
                        }
                        if (notFoundMatch) {
                            overallStats.talentsNotFound += parseInt(notFoundMatch[1]);
                        }
                        console.log(`🔧 [Backend] Successfully fixed event: ${event.name}`);
                    }
                    else {
                        overallStats.failedFixes++;
                        console.error(`🔧 [Backend] Failed to fix event: ${event.name}`);
                    }
                }
                catch (error) {
                    overallStats.failedFixes++;
                    console.error(`🔧 [Backend] Error fixing event ${event.name}:`, error);
                }
                overallStats.totalProcessed++;
            }
            const yearInfo = year ? ` (${year}+ events only)` : ' (all events)';
            const successMessage = `Aggregated batch fix completed${yearInfo}: processed ${overallStats.totalProcessed} events, ${overallStats.successfulFixes} successful, ${overallStats.failedFixes} failed, added ${overallStats.relationshipsAdded} relationships, removed ${overallStats.relationshipsRemoved} relationships, ${overallStats.talentsNotFound} talents not found`;
            console.log(`🔧 [Backend] ${successMessage}`);
            return {
                success: true,
                message: successMessage,
                stats: overallStats
            };
        }
        catch (error) {
            console.error('🔧 [Backend] Error in aggregated batch fix:', error);
            throw error;
        }
    }
    async fixEventTalentRelationshipsFromCsv() {
        console.log('🔧 [Backend] Starting CSV-based event-talent relationship fix with Azure validation...');
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        let transactionStarted = false;
        let azurePool = null;
        try {
            const azureConfig = {
                server: 'mssql.drv1.umbhost.net',
                database: 'TLM',
                user: 'Reader',
                password: 'TLM1234!',
                options: {
                    encrypt: false
                }
            };
            console.log('🔗 [Backend] Connecting to Azure MSSQL for validation...');
            azurePool = await sql.connect(azureConfig);
            console.log('✅ [Backend] Connected to Azure MSSQL successfully');
            const csvPath = path.join(process.cwd(), 'data/imports/talentjoins.csv');
            console.log('📁 [Backend] Reading CSV from:', csvPath);
            const csvContent = fs.readFileSync(csvPath, 'utf-8');
            const lines = csvContent.trim().split('\n');
            const header = lines[0];
            const dataLines = lines.slice(1);
            console.log(`📊 [Backend] CSV contains ${dataLines.length} relationships`);
            const csvRelationships = dataLines.map((line, index) => {
                const [eventGuid, talentGuid] = line.split(',').map(id => id.trim());
                const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
                if (!guidRegex.test(eventGuid) || !guidRegex.test(talentGuid)) {
                    console.warn(`⚠️ [Backend] Invalid GUID format at line ${index + 2}: Event=${eventGuid}, Talent=${talentGuid}`);
                    return null;
                }
                return {
                    eventGuid: eventGuid.toLowerCase(),
                    talentGuid: talentGuid.toLowerCase()
                };
            }).filter(rel => rel !== null);
            console.log(`✅ [Backend] Parsed ${csvRelationships.length} valid GUID relationships`);
            console.log('🔍 [Backend] Validating relationships against Azure MSSQL source...');
            const validatedRelationships = [];
            let invalidEventCount = 0;
            let invalidTalentCount = 0;
            let validRelationshipCount = 0;
            for (const relationship of csvRelationships) {
                const azureEventResult = await azurePool.request()
                    .input('eventId', sql.UniqueIdentifier, relationship.eventGuid)
                    .query('SELECT Id FROM PerformanceEvents WHERE Id = @eventId');
                if (azureEventResult.recordset.length === 0) {
                    invalidEventCount++;
                    console.warn(`⚠️ [Backend] Event GUID not found in Azure MSSQL: ${relationship.eventGuid}`);
                    continue;
                }
                const azureTalentResult = await azurePool.request()
                    .input('talentId', sql.UniqueIdentifier, relationship.talentGuid)
                    .query('SELECT Id FROM Talent WHERE Id = @talentId');
                if (azureTalentResult.recordset.length === 0) {
                    invalidTalentCount++;
                    console.warn(`⚠️ [Backend] Talent GUID not found in Azure MSSQL: ${relationship.talentGuid}`);
                    continue;
                }
                const azureRelationshipResult = await azurePool.request()
                    .input('eventId', sql.UniqueIdentifier, relationship.eventGuid)
                    .input('talentId', sql.UniqueIdentifier, relationship.talentGuid)
                    .query('SELECT PerformanceEventsId, TalentListId FROM PerformanceEventModelTalentModel WHERE PerformanceEventsId = @eventId AND TalentListId = @talentId');
                if (azureRelationshipResult.recordset.length === 0) {
                    console.warn(`⚠️ [Backend] Relationship not found in Azure MSSQL junction table: Event=${relationship.eventGuid}, Talent=${relationship.talentGuid}`);
                    continue;
                }
                validatedRelationships.push(relationship);
                validRelationshipCount++;
            }
            console.log(`✅ [Backend] Azure validation complete:`);
            console.log(`   - Valid relationships: ${validRelationshipCount}`);
            console.log(`   - Invalid events: ${invalidEventCount}`);
            console.log(`   - Invalid talents: ${invalidTalentCount}`);
            if (validatedRelationships.length === 0) {
                return {
                    success: true,
                    message: 'No valid relationships found after Azure validation',
                    stats: {
                        totalCsvRelationships: csvRelationships.length,
                        validatedRelationships: 0,
                        invalidEventCount,
                        invalidTalentCount,
                        relationshipsInserted: 0
                    }
                };
            }
            console.log('🗺️ [Backend] Mapping Azure GUIDs to PostgreSQL IDs...');
            const eventsWithAzureIds = await queryRunner.manager.find(events_entity_1.Event, {
                where: { azure_id: (0, typeorm_2.Not)((0, typeorm_2.IsNull)()) },
                select: ['id', 'azure_id']
            });
            const talentsWithAzureIds = await queryRunner.manager.find(talent_entity_1.Talent, {
                where: { azure_id: (0, typeorm_2.Not)((0, typeorm_2.IsNull)()) },
                select: ['id', 'azure_id']
            });
            const eventAzureToPostgresMap = new Map();
            eventsWithAzureIds.forEach(event => {
                if (event.azure_id) {
                    eventAzureToPostgresMap.set(event.azure_id.toLowerCase(), event.id);
                }
            });
            const talentAzureToPostgresMap = new Map();
            talentsWithAzureIds.forEach(talent => {
                if (talent.azure_id) {
                    talentAzureToPostgresMap.set(talent.azure_id.toLowerCase(), talent.id.toString());
                }
            });
            console.log(`🗺️ [Backend] Event mapping: ${eventAzureToPostgresMap.size} events`);
            console.log(`🗺️ [Backend] Talent mapping: ${talentAzureToPostgresMap.size} talents`);
            const existingRelationships = await queryRunner.manager.find(event_talent_entity_1.EventTalent, {
                select: ['eventId', 'talentId']
            });
            const existingRelationshipSet = new Set();
            existingRelationships.forEach(rel => {
                existingRelationshipSet.add(`${rel.eventId}-${rel.talentId}`);
            });
            console.log(`📋 [Backend] Found ${existingRelationships.length} existing relationships`);
            const relationshipsToInsert = [];
            let skippedMissingEvent = 0;
            let skippedMissingTalent = 0;
            let skippedExisting = 0;
            for (const validatedRel of validatedRelationships) {
                const postgresEventId = eventAzureToPostgresMap.get(validatedRel.eventGuid);
                const postgresTalentId = talentAzureToPostgresMap.get(validatedRel.talentGuid);
                if (!postgresEventId) {
                    skippedMissingEvent++;
                    console.warn(`⚠️ [Backend] Event not found in PostgreSQL: ${validatedRel.eventGuid}`);
                    continue;
                }
                if (!postgresTalentId) {
                    skippedMissingTalent++;
                    console.warn(`⚠️ [Backend] Talent not found in PostgreSQL: ${validatedRel.talentGuid}`);
                    continue;
                }
                const relationshipKey = `${postgresEventId}-${postgresTalentId}`;
                if (existingRelationshipSet.has(relationshipKey)) {
                    skippedExisting++;
                    continue;
                }
                relationshipsToInsert.push({
                    eventId: postgresEventId,
                    talentId: postgresTalentId
                });
            }
            console.log(`📊 [Backend] Relationships to insert: ${relationshipsToInsert.length}`);
            console.log(`⏭️ [Backend] Skipped - missing event in PostgreSQL: ${skippedMissingEvent}`);
            console.log(`⏭️ [Backend] Skipped - missing talent in PostgreSQL: ${skippedMissingTalent}`);
            console.log(`⏭️ [Backend] Skipped - already exists: ${skippedExisting}`);
            if (relationshipsToInsert.length === 0) {
                return {
                    success: true,
                    message: 'No new relationships to insert after validation and mapping',
                    stats: {
                        totalCsvRelationships: csvRelationships.length,
                        validatedRelationships: validatedRelationships.length,
                        relationshipsInserted: 0,
                        skippedMissingEvent,
                        skippedMissingTalent,
                        skippedExisting,
                        invalidEventCount,
                        invalidTalentCount
                    }
                };
            }
            await queryRunner.startTransaction();
            transactionStarted = true;
            const batchSize = 1000;
            let totalInserted = 0;
            for (let i = 0; i < relationshipsToInsert.length; i += batchSize) {
                const batch = relationshipsToInsert.slice(i, i + batchSize);
                const values = batch.map(() => '(?, ?)').join(', ');
                const params = batch.flatMap(rel => [rel.eventId, rel.talentId]);
                const result = await queryRunner.query(`
          INSERT INTO event_talents (event_id, talent_id)
          VALUES ${values}
          ON CONFLICT (event_id, talent_id) DO NOTHING
          RETURNING event_id, talent_id
        `, params);
                const actualInserted = result.length;
                totalInserted += actualInserted;
                console.log(`💾 [Backend] Processed batch ${Math.floor(i / batchSize) + 1}: ${actualInserted}/${batch.length} relationships inserted (Total: ${totalInserted})`);
            }
            await queryRunner.commitTransaction();
            console.log(`✅ [Backend] CSV-based fix completed successfully with Azure validation`);
            console.log(`📊 [Backend] Final stats: ${totalInserted} relationships inserted`);
            return {
                success: true,
                message: `Successfully processed CSV data with Azure validation and inserted ${totalInserted} new event-talent relationships`,
                stats: {
                    totalCsvRelationships: csvRelationships.length,
                    validatedRelationships: validatedRelationships.length,
                    relationshipsInserted: totalInserted,
                    skippedMissingEvent,
                    skippedMissingTalent,
                    skippedExisting,
                    invalidEventCount,
                    invalidTalentCount
                }
            };
        }
        catch (error) {
            console.error('🔧 [Backend] Error in fixEventTalentRelationshipsFromCsv:', error);
            if (transactionStarted) {
                await queryRunner.rollbackTransaction();
            }
            throw error;
        }
        finally {
            if (azurePool) {
                await azurePool.close();
                console.log('🔗 [Backend] Azure MSSQL connection closed');
            }
            await queryRunner.release();
        }
    }
    async fixAllEventTalentRelationships() {
        let azurePool = null;
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            console.log('🔧 [Backend] Starting bulk fix for all event-talent relationships');
            try {
                azurePool = await this.getAzurePool();
                console.log('🔧 [Backend] Successfully connected to Azure SQL');
            }
            catch (azureError) {
                console.error('🔧 [Backend] Failed to connect to Azure SQL:', azureError);
                throw new Error('Azure SQL connection failed');
            }
            const stats = {
                totalProcessed: 0,
                relationshipsAdded: 0,
                relationshipsRemoved: 0,
                talentsNotFound: 0,
                eventsNotFound: 0,
                errors: 0
            };
            const pgEvents = await queryRunner.query(`
        SELECT id, azure_id, name
        FROM event
        WHERE deleted = false AND azure_id IS NOT NULL
        LIMIT 500
      `);
            console.log(`🔧 [Backend] Found ${pgEvents.length} events in PostgreSQL with Azure IDs`);
            if (pgEvents.length === 0) {
                console.log('🔧 [Backend] No events found with Azure IDs, nothing to process');
                await queryRunner.commitTransaction();
                return {
                    success: true,
                    message: 'No events found with Azure IDs to process'
                };
            }
            const pgTalents = await queryRunner.query(`
        SELECT id, azure_id, name
        FROM talent
        WHERE deleted = false AND azure_id IS NOT NULL
      `);
            console.log(`🔧 [Backend] Found ${pgTalents.length} talents in PostgreSQL with Azure IDs`);
            const talentAzureToPostgresMap = new Map();
            pgTalents.forEach(talent => {
                if (talent.azure_id) {
                    talentAzureToPostgresMap.set(talent.azure_id.toLowerCase(), talent.id);
                }
            });
            const currentRelationships = await queryRunner.query(`
        SELECT event_id, talent_id FROM event_talents
      `);
            const relationshipMap = new Map();
            currentRelationships.forEach(rel => {
                if (!relationshipMap.has(rel.event_id)) {
                    relationshipMap.set(rel.event_id, new Set());
                }
                relationshipMap.get(rel.event_id).add(rel.talent_id);
            });
            console.log(`🔧 [Backend] Starting to process ${pgEvents.length} events`);
            for (let i = 0; i < pgEvents.length; i++) {
                const event = pgEvents[i];
                try {
                    if (i % 50 === 0 || i === pgEvents.length - 1) {
                        console.log(`🔧 [Backend] Processing event ${i + 1}/${pgEvents.length}: ${event.name}`);
                    }
                    if (!event.azure_id) {
                        stats.eventsNotFound++;
                        continue;
                    }
                    let azureResult;
                    try {
                        azureResult = await azurePool.request()
                            .input('eventId', sql.UniqueIdentifier, event.azure_id)
                            .query(`
                SELECT t.Id as talentId
                FROM PerformanceEventModelTalentModel pemtm
                JOIN Talent t ON pemtm.TalentListId = t.Id
                WHERE pemtm.PerformanceEventsId = @eventId
              `);
                    }
                    catch (azureQueryError) {
                        console.error(`🔧 [Backend] Failed to query Azure for event ${event.name}:`, azureQueryError);
                        stats.errors++;
                        continue;
                    }
                    const azureTalentIds = azureResult.recordset.map(t => t.talentId.toLowerCase());
                    const matchedTalentIds = [];
                    for (const azureTalentId of azureTalentIds) {
                        const pgTalentId = talentAzureToPostgresMap.get(azureTalentId);
                        if (pgTalentId) {
                            matchedTalentIds.push(pgTalentId);
                        }
                        else {
                            stats.talentsNotFound++;
                        }
                    }
                    const currentEventTalentIds = relationshipMap.get(event.id) || new Set();
                    const talentsToAdd = matchedTalentIds.filter(id => !currentEventTalentIds.has(id));
                    const talentsToRemove = Array.from(currentEventTalentIds)
                        .filter(id => !matchedTalentIds.includes(id));
                    if (talentsToAdd.length > 0) {
                        for (const talentId of talentsToAdd) {
                            try {
                                await queryRunner.query(`
                  INSERT INTO event_talents (event_id, talent_id)
                  VALUES ($1, $2)
                  ON CONFLICT DO NOTHING
                `, [event.id, talentId]);
                                stats.relationshipsAdded++;
                            }
                            catch (error) {
                                console.error(`Error adding talent relationship: ${error instanceof Error ? error.message : 'Unknown error'}`);
                                stats.errors++;
                            }
                        }
                    }
                    if (talentsToRemove.length > 0) {
                        for (const talentId of talentsToRemove) {
                            try {
                                await queryRunner.query(`
                  DELETE FROM event_talents 
                  WHERE event_id = $1 AND talent_id = $2
                `, [event.id, talentId]);
                                stats.relationshipsRemoved++;
                            }
                            catch (error) {
                                console.error(`Error removing talent relationship: ${error instanceof Error ? error.message : 'Unknown error'}`);
                                stats.errors++;
                            }
                        }
                    }
                    stats.totalProcessed++;
                }
                catch (error) {
                    console.error(`🔧 [Backend] Error processing event ${event.name} (${event.id}): ${error instanceof Error ? error.message : 'Unknown error'}`);
                    stats.errors++;
                }
            }
            console.log(`🔧 [Backend] Bulk fix completed successfully`);
            console.log(`🔧 [Backend] Final stats: processed ${stats.totalProcessed} events, added ${stats.relationshipsAdded} relationships, removed ${stats.relationshipsRemoved} relationships, ${stats.talentsNotFound} talents not found, ${stats.errors} errors`);
            await queryRunner.commitTransaction();
            return {
                success: true,
                message: `Processed ${stats.totalProcessed} events, added ${stats.relationshipsAdded} talent relationships, removed ${stats.relationshipsRemoved} talent relationships, ${stats.talentsNotFound} talents not found, ${stats.errors} errors`
            };
        }
        catch (error) {
            console.error('🔧 [Backend] Error in fixAllEventTalentRelationships:', error);
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
};
exports.EventTalentRelationsController = EventTalentRelationsController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('year')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EventTalentRelationsController.prototype, "getEventTalentStatusCounts", null);
__decorate([
    (0, common_1.Get)('status'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EventTalentRelationsController.prototype, "getEventTalentStatusCountsStatus", null);
__decorate([
    (0, common_1.Get)('missing'),
    __param(0, (0, common_1.Query)('year')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EventTalentRelationsController.prototype, "getEventsWithMissingTalents", null);
__decorate([
    (0, common_1.Get)('mismatched'),
    __param(0, (0, common_1.Query)('year')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EventTalentRelationsController.prototype, "getEventsWithMismatchedTalents", null);
__decorate([
    (0, common_1.Post)('fix'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [UpdateEventTalentsDto]),
    __metadata("design:returntype", Promise)
], EventTalentRelationsController.prototype, "fixEventTalentRelationship", null);
__decorate([
    (0, common_1.Post)('fix/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EventTalentRelationsController.prototype, "fixSingleEventTalentRelationship", null);
__decorate([
    (0, common_1.Post)('fix-all-aggregated'),
    __param(0, (0, common_1.Query)('year')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EventTalentRelationsController.prototype, "fixAllEventTalentRelationshipsAggregated", null);
__decorate([
    (0, common_1.Post)('fix-from-csv'),
    (0, swagger_1.ApiOperation)({ summary: 'Fix event-talent relationships using CSV data with Azure MSSQL validation' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Event-talent relationships fixed successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EventTalentRelationsController.prototype, "fixEventTalentRelationshipsFromCsv", null);
__decorate([
    (0, common_1.Post)('fix-all'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EventTalentRelationsController.prototype, "fixAllEventTalentRelationships", null);
exports.EventTalentRelationsController = EventTalentRelationsController = __decorate([
    (0, common_1.Controller)('admin/event-talent-relations'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, typeorm_1.InjectDataSource)()),
    __metadata("design:paramtypes", [typeorm_2.DataSource])
], EventTalentRelationsController);
//# sourceMappingURL=event-talent-relations.controller.js.map