/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/events/route";
exports.ids = ["app/api/events/route"];
exports.modules = {

/***/ "(rsc)/./app/api/events/route.ts":
/*!*********************************!*\
  !*** ./app/api/events/route.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic),\n/* harmony export */   fetchCache: () => (/* binding */ fetchCache)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/config */ \"(rsc)/./lib/config.ts\");\n\n\n\nconst dynamic = 'force-dynamic';\nconst fetchCache = 'force-no-store';\n// Request deduplication for identical requests\nconst pendingRequests = new Map();\n// Retry configuration\nconst MAX_RETRIES = 5;\nconst INITIAL_RETRY_DELAY = 500;\nconst MAX_RETRY_DELAY = 10000;\nconst REQUEST_TIMEOUT = 15000;\n// Database connection error patterns\nconst DB_CONNECTION_ERROR_PATTERNS = [\n    'timeout exceeded when trying to connect',\n    'connection refused',\n    'ECONNREFUSED',\n    'database connection error',\n    'too many connections',\n    'connection terminated'\n];\n// Check if error is a database connection issue\nconst isDatabaseConnectionError = (error)=>{\n    const errorMessage = error?.message || error?.toString() || '';\n    return DB_CONNECTION_ERROR_PATTERNS.some((pattern)=>errorMessage.toLowerCase().includes(pattern.toLowerCase()));\n};\nasync function GET(request) {\n    const headersList = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)();\n    const host = headersList.get('host') || '';\n    const apiUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_2__.getServerApiUrl)(host);\n    // Retry with exponential backoff\n    const fetchWithRetry = async (url, options, retries = MAX_RETRIES, delay = INITIAL_RETRY_DELAY)=>{\n        try {\n            // Standard fetch with retry logic\n            const response = await fetch(url, options);\n            if (!response.ok) {\n                // If we get a 500 error and have retries left, retry the request\n                if (response.status === 500 && retries > 0) {\n                    console.log(`Retrying fetch (${MAX_RETRIES - retries + 1}/${MAX_RETRIES}) after ${delay}ms...`);\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                    const nextDelay = Math.min(delay * 2, MAX_RETRY_DELAY);\n                    return fetchWithRetry(url, options, retries - 1, nextDelay);\n                }\n                const errorText = await response.text().catch(()=>'');\n                if (retries > 0 && (response.status === 429 || isDatabaseConnectionError({\n                    message: errorText\n                }))) {\n                    console.log(`Rate limited or DB connection issue (${MAX_RETRIES - retries + 1}/${MAX_RETRIES}). Retrying after ${delay}ms...`);\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                    const nextDelay = Math.min(delay * 2, MAX_RETRY_DELAY);\n                    return fetchWithRetry(url, options, retries - 1, nextDelay);\n                }\n                throw new Error(`Failed to fetch events: ${response.statusText || response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            if (retries > 0 && isDatabaseConnectionError(error)) {\n                console.log(`Database connection error (${MAX_RETRIES - retries + 1}/${MAX_RETRIES}). Retrying after ${delay}ms...`);\n                await new Promise((resolve)=>setTimeout(resolve, delay));\n                const nextDelay = Math.min(delay * 2, MAX_RETRY_DELAY);\n                return fetchWithRetry(url, options, retries - 1, nextDelay);\n            }\n            throw error;\n        }\n    };\n    try {\n        // Get query parameters from request URL\n        const { searchParams } = new URL(request.url);\n        const page = searchParams.get('page') || '1';\n        const limit = searchParams.get('limit') || '20';\n        const date = searchParams.get('date') || '';\n        const venue = searchParams.get('venue') || '';\n        const talent = searchParams.get('talent') || '';\n        const include = searchParams.get('include') || '';\n        const search = searchParams.get('search') || '';\n        const genre = searchParams.get('genre') || '';\n        const includeFeatured = searchParams.get('includeFeatured') || '';\n        // Construct query string\n        const queryParams = new URLSearchParams();\n        if (page) queryParams.set('page', page);\n        if (limit) queryParams.set('limit', limit);\n        if (date) queryParams.set('date', date);\n        if (venue) queryParams.set('venue', venue);\n        if (talent) queryParams.set('talent', talent);\n        if (include) queryParams.set('include', include);\n        if (search) queryParams.set('search', search);\n        if (genre) queryParams.set('genre', genre);\n        if (includeFeatured) queryParams.set('includeFeatured', includeFeatured);\n        // Construct API URL\n        const url = `${apiUrl}/events?${queryParams.toString()}`;\n        console.log(`Fetching events from: ${url}`);\n        // Check for pending request deduplication\n        const requestKey = `events:${queryParams.toString()}`;\n        if (pendingRequests.has(requestKey)) {\n            console.log(`Deduplicating identical request: ${requestKey}`);\n            const result = await pendingRequests.get(requestKey);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n        }\n        // Create AbortController for timeout\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), REQUEST_TIMEOUT);\n        try {\n            // Create and store the request promise for deduplication\n            const requestPromise = fetchWithRetry(url, {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                signal: controller.signal\n            });\n            pendingRequests.set(requestKey, requestPromise);\n            // Fetch events with retry logic\n            const data = await requestPromise;\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n        } finally{\n            clearTimeout(timeoutId);\n            // Clean up the pending request\n            pendingRequests.delete(requestKey);\n        }\n    } catch (error) {\n        console.error('Error in /api/events:', error);\n        // Check if it's an abort error (timeout)\n        if (error.name === 'AbortError') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Request timeout. The server took too long to respond.'\n            }, {\n                status: 504\n            });\n        }\n        // Check if it's a rate limit error\n        if (error.message?.includes('429') || error.message?.includes('Too Many Requests')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Rate limit exceeded. Please try again later.'\n            }, {\n                status: 429\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Failed to fetch events'\n        }, {\n            status: 503\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/events/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/config.ts":
/*!***********************!*\
  !*** ./lib/config.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl),\n/* harmony export */   getServerApiUrl: () => (/* binding */ getServerApiUrl)\n/* harmony export */ });\n// Debug environment variables - removed for production\nif (true) {}\n// Define the correct API URLs (allow explicit override via env)\nconst API_URLS = {\n    development: process.env.NEXT_PUBLIC_API_BASE_URL || ( true ? 'http://localhost:4000' : 0),\n    production: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://tucsonlovesmusic-api-aad3484ec0df.herokuapp.com',\n    local: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4000'\n};\n// Auth0 configuration\nconst getAuth0Config = ()=>{\n    // Hardcode tenant configuration to avoid env drift in deploys\n    return {\n        domain: 'dev-q8s86rgn7vmcvrco.us.auth0.com',\n        clientId: 'tbboBdkgReB764L9HtHav4VADfsu6txM',\n        audience: 'https://tucsonlovesmusic.com/api',\n        scope: 'openid profile email read:admin write:admin offline_access read:events read:venues write:venues venue:access'\n    };\n};\n// Stripe configuration\nconst getStripeConfig = ()=>{\n    const publishableKey = \"pk_test_51QT8OhRxBJaRlFvtGBl4Q2iOaQBRTdAWQb5bKbbxf7nYzJ6OnanRqiU2hdVcTfB6hVfV1GeuhE6HCZRIPDvvYGu000oGj27BdQ\";\n    if (!publishableKey) {\n        throw new Error('Missing required Stripe configuration');\n    }\n    return {\n        publishableKey\n    };\n};\n// Determine current environment\nconst getEnvironment = ()=>{\n    const env = \"local\" || 0 || 0;\n    return env === 'production' ? 'production' : 'development';\n};\n// Configuration object\nconst config = {\n    apiBaseUrl: API_URLS[getEnvironment()],\n    deployEnv: getEnvironment(),\n    auth0: getAuth0Config(),\n    stripe: getStripeConfig()\n};\n// Server-side API URL function\nconst getServerApiUrl = (hostname)=>{\n    // If hostname is provided, use it to determine the environment\n    if (hostname) {\n        if (hostname.includes('dev-tucsonlovesmusic.netlify.app')) {\n            return API_URLS.development;\n        }\n        if (hostname.includes('tucsonlovesmusic.netlify.app') && !hostname.includes('dev-')) {\n            return API_URLS.production;\n        }\n    }\n    // Default to local or development\n    return config.apiBaseUrl;\n};\n// Client-side API URL function\nconst getApiUrl = ()=>{\n    // Check if we're in a browser environment\n    if (false) {}\n    // Default to environment-based configuration\n    const deployEnv = \"local\" || 0;\n    return deployEnv === 'production' ? API_URLS.production : API_URLS.development;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,******************************************************************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\n//# sourceURL=webpack-internal:///(rsc)/./lib/config.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fevents%2Froute&page=%2Fapi%2Fevents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fevents%2Froute.ts&appDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fevents%2Froute&page=%2Fapi%2Fevents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fevents%2Froute.ts&appDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_roxas_OneDrive_Desktop_PROJECTS_tucsonlovesmusic_com_Prototype13_frontend_app_api_events_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/events/route.ts */ \"(rsc)/./app/api/events/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/events/route\",\n        pathname: \"/api/events\",\n        filename: \"route\",\n        bundlePath: \"app/api/events/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\app\\\\api\\\\events\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_roxas_OneDrive_Desktop_PROJECTS_tucsonlovesmusic_com_Prototype13_frontend_app_api_events_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fevents%2Froute&page=%2Fapi%2Fevents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fevents%2Froute.ts&appDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fevents%2Froute&page=%2Fapi%2Fevents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fevents%2Froute.ts&appDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();