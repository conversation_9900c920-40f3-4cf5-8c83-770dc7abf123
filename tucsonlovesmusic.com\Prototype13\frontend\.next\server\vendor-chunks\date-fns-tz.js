"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/date-fns-tz";
exports.ids = ["vendor-chunks/date-fns-tz"];
exports.modules = {

/***/ "(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/getTimezoneOffsetInMilliseconds/index.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/date-fns-tz/dist/esm/_lib/getTimezoneOffsetInMilliseconds/index.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTimezoneOffsetInMilliseconds: () => (/* binding */ getTimezoneOffsetInMilliseconds)\n/* harmony export */ });\n/**\n * Google Chrome as of 67.0.3396.87 introduced timezones with offset that includes seconds.\n * They usually appear for dates that denote time before the timezones were introduced\n * (e.g. for 'Europe/Prague' timezone the offset is GMT+00:57:44 before 1 October 1891\n * and GMT+01:00:00 after that date)\n *\n * Date#getTimezoneOffset returns the offset in minutes and would return 57 for the example above,\n * which would lead to incorrect calculations.\n *\n * This function returns the timezone offset in milliseconds that takes seconds in account.\n */\nfunction getTimezoneOffsetInMilliseconds(date) {\n    const utcDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds()));\n    utcDate.setUTCFullYear(date.getFullYear());\n    return +date - +utcDate;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZGF0ZS1mbnMtdHovZGlzdC9lc20vX2xpYi9nZXRUaW1lem9uZU9mZnNldEluTWlsbGlzZWNvbmRzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyb3hhc1xcT25lRHJpdmVcXERlc2t0b3BcXFBST0pFQ1RTXFx0dWNzb25sb3Zlc211c2ljLmNvbVxcUHJvdG90eXBlMTNcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGRhdGUtZm5zLXR6XFxkaXN0XFxlc21cXF9saWJcXGdldFRpbWV6b25lT2Zmc2V0SW5NaWxsaXNlY29uZHNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogR29vZ2xlIENocm9tZSBhcyBvZiA2Ny4wLjMzOTYuODcgaW50cm9kdWNlZCB0aW1lem9uZXMgd2l0aCBvZmZzZXQgdGhhdCBpbmNsdWRlcyBzZWNvbmRzLlxuICogVGhleSB1c3VhbGx5IGFwcGVhciBmb3IgZGF0ZXMgdGhhdCBkZW5vdGUgdGltZSBiZWZvcmUgdGhlIHRpbWV6b25lcyB3ZXJlIGludHJvZHVjZWRcbiAqIChlLmcuIGZvciAnRXVyb3BlL1ByYWd1ZScgdGltZXpvbmUgdGhlIG9mZnNldCBpcyBHTVQrMDA6NTc6NDQgYmVmb3JlIDEgT2N0b2JlciAxODkxXG4gKiBhbmQgR01UKzAxOjAwOjAwIGFmdGVyIHRoYXQgZGF0ZSlcbiAqXG4gKiBEYXRlI2dldFRpbWV6b25lT2Zmc2V0IHJldHVybnMgdGhlIG9mZnNldCBpbiBtaW51dGVzIGFuZCB3b3VsZCByZXR1cm4gNTcgZm9yIHRoZSBleGFtcGxlIGFib3ZlLFxuICogd2hpY2ggd291bGQgbGVhZCB0byBpbmNvcnJlY3QgY2FsY3VsYXRpb25zLlxuICpcbiAqIFRoaXMgZnVuY3Rpb24gcmV0dXJucyB0aGUgdGltZXpvbmUgb2Zmc2V0IGluIG1pbGxpc2Vjb25kcyB0aGF0IHRha2VzIHNlY29uZHMgaW4gYWNjb3VudC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldFRpbWV6b25lT2Zmc2V0SW5NaWxsaXNlY29uZHMoZGF0ZSkge1xuICAgIGNvbnN0IHV0Y0RhdGUgPSBuZXcgRGF0ZShEYXRlLlVUQyhkYXRlLmdldEZ1bGxZZWFyKCksIGRhdGUuZ2V0TW9udGgoKSwgZGF0ZS5nZXREYXRlKCksIGRhdGUuZ2V0SG91cnMoKSwgZGF0ZS5nZXRNaW51dGVzKCksIGRhdGUuZ2V0U2Vjb25kcygpLCBkYXRlLmdldE1pbGxpc2Vjb25kcygpKSk7XG4gICAgdXRjRGF0ZS5zZXRVVENGdWxsWWVhcihkYXRlLmdldEZ1bGxZZWFyKCkpO1xuICAgIHJldHVybiArZGF0ZSAtICt1dGNEYXRlO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/getTimezoneOffsetInMilliseconds/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/newDateUTC/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/date-fns-tz/dist/esm/_lib/newDateUTC/index.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   newDateUTC: () => (/* binding */ newDateUTC)\n/* harmony export */ });\n/**\n * Use instead of `new Date(Date.UTC(...))` to support years below 100 which doesn't work\n * otherwise due to the nature of the\n * [`Date` constructor](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date#interpretation_of_two-digit_years.\n *\n * For `Date.UTC(...)`, use `newDateUTC(...).getTime()`.\n */\nfunction newDateUTC(fullYear, month, day, hour, minute, second, millisecond) {\n    const utcDate = new Date(0);\n    utcDate.setUTCFullYear(fullYear, month, day);\n    utcDate.setUTCHours(hour, minute, second, millisecond);\n    return utcDate;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZGF0ZS1mbnMtdHovZGlzdC9lc20vX2xpYi9uZXdEYXRlVVRDL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyb3hhc1xcT25lRHJpdmVcXERlc2t0b3BcXFBST0pFQ1RTXFx0dWNzb25sb3Zlc211c2ljLmNvbVxcUHJvdG90eXBlMTNcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGRhdGUtZm5zLXR6XFxkaXN0XFxlc21cXF9saWJcXG5ld0RhdGVVVENcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVXNlIGluc3RlYWQgb2YgYG5ldyBEYXRlKERhdGUuVVRDKC4uLikpYCB0byBzdXBwb3J0IHllYXJzIGJlbG93IDEwMCB3aGljaCBkb2Vzbid0IHdvcmtcbiAqIG90aGVyd2lzZSBkdWUgdG8gdGhlIG5hdHVyZSBvZiB0aGVcbiAqIFtgRGF0ZWAgY29uc3RydWN0b3JdKGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2VuLVVTL2RvY3MvV2ViL0phdmFTY3JpcHQvUmVmZXJlbmNlL0dsb2JhbF9PYmplY3RzL0RhdGUjaW50ZXJwcmV0YXRpb25fb2ZfdHdvLWRpZ2l0X3llYXJzLlxuICpcbiAqIEZvciBgRGF0ZS5VVEMoLi4uKWAsIHVzZSBgbmV3RGF0ZVVUQyguLi4pLmdldFRpbWUoKWAuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBuZXdEYXRlVVRDKGZ1bGxZZWFyLCBtb250aCwgZGF5LCBob3VyLCBtaW51dGUsIHNlY29uZCwgbWlsbGlzZWNvbmQpIHtcbiAgICBjb25zdCB1dGNEYXRlID0gbmV3IERhdGUoMCk7XG4gICAgdXRjRGF0ZS5zZXRVVENGdWxsWWVhcihmdWxsWWVhciwgbW9udGgsIGRheSk7XG4gICAgdXRjRGF0ZS5zZXRVVENIb3Vycyhob3VyLCBtaW51dGUsIHNlY29uZCwgbWlsbGlzZWNvbmQpO1xuICAgIHJldHVybiB1dGNEYXRlO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/newDateUTC/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/tzIntlTimeZoneName/index.js":
/*!****************************************************************************!*\
  !*** ./node_modules/date-fns-tz/dist/esm/_lib/tzIntlTimeZoneName/index.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tzIntlTimeZoneName: () => (/* binding */ tzIntlTimeZoneName)\n/* harmony export */ });\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/getDefaultOptions.mjs\");\n\n/**\n * Returns the formatted time zone name of the provided `timeZone` or the current\n * system time zone if omitted, accounting for DST according to the UTC value of\n * the date.\n */\nfunction tzIntlTimeZoneName(length, date, options) {\n    const defaultOptions = (0,date_fns__WEBPACK_IMPORTED_MODULE_0__.getDefaultOptions)();\n    const dtf = getDTF(length, options.timeZone, options.locale ?? defaultOptions.locale);\n    return 'formatToParts' in dtf ? partsTimeZone(dtf, date) : hackyTimeZone(dtf, date);\n}\nfunction partsTimeZone(dtf, date) {\n    const formatted = dtf.formatToParts(date);\n    for (let i = formatted.length - 1; i >= 0; --i) {\n        if (formatted[i].type === 'timeZoneName') {\n            return formatted[i].value;\n        }\n    }\n    return undefined;\n}\nfunction hackyTimeZone(dtf, date) {\n    const formatted = dtf.format(date).replace(/\\u200E/g, '');\n    const tzNameMatch = / [\\w-+ ]+$/.exec(formatted);\n    return tzNameMatch ? tzNameMatch[0].substr(1) : '';\n}\n// If a locale has been provided `en-US` is used as a fallback in case it is an\n// invalid locale, otherwise the locale is left undefined to use the system locale.\nfunction getDTF(length, timeZone, locale) {\n    return new Intl.DateTimeFormat(locale ? [locale.code, 'en-US'] : undefined, {\n        timeZone: timeZone,\n        timeZoneName: length,\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/tzIntlTimeZoneName/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/tzParseTimezone/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/date-fns-tz/dist/esm/_lib/tzParseTimezone/index.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tzParseTimezone: () => (/* binding */ tzParseTimezone)\n/* harmony export */ });\n/* harmony import */ var _tzTokenizeDate_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../tzTokenizeDate/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/tzTokenizeDate/index.js\");\n/* harmony import */ var _newDateUTC_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../newDateUTC/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/newDateUTC/index.js\");\n\n\nconst MILLISECONDS_IN_HOUR = 3600000;\nconst MILLISECONDS_IN_MINUTE = 60000;\nconst patterns = {\n    timezone: /([Z+-].*)$/,\n    timezoneZ: /^(Z)$/,\n    timezoneHH: /^([+-]\\d{2})$/,\n    timezoneHHMM: /^([+-])(\\d{2}):?(\\d{2})$/,\n};\n// Parse constious time zone offset formats to an offset in milliseconds\nfunction tzParseTimezone(timezoneString, date, isUtcDate) {\n    // Empty string\n    if (!timezoneString) {\n        return 0;\n    }\n    // Z\n    let token = patterns.timezoneZ.exec(timezoneString);\n    if (token) {\n        return 0;\n    }\n    let hours;\n    let absoluteOffset;\n    // ±hh\n    token = patterns.timezoneHH.exec(timezoneString);\n    if (token) {\n        hours = parseInt(token[1], 10);\n        if (!validateTimezone(hours)) {\n            return NaN;\n        }\n        return -(hours * MILLISECONDS_IN_HOUR);\n    }\n    // ±hh:mm or ±hhmm\n    token = patterns.timezoneHHMM.exec(timezoneString);\n    if (token) {\n        hours = parseInt(token[2], 10);\n        const minutes = parseInt(token[3], 10);\n        if (!validateTimezone(hours, minutes)) {\n            return NaN;\n        }\n        absoluteOffset = Math.abs(hours) * MILLISECONDS_IN_HOUR + minutes * MILLISECONDS_IN_MINUTE;\n        return token[1] === '+' ? -absoluteOffset : absoluteOffset;\n    }\n    // IANA time zone\n    if (isValidTimezoneIANAString(timezoneString)) {\n        date = new Date(date || Date.now());\n        const utcDate = isUtcDate ? date : toUtcDate(date);\n        const offset = calcOffset(utcDate, timezoneString);\n        const fixedOffset = isUtcDate ? offset : fixOffset(date, offset, timezoneString);\n        return -fixedOffset;\n    }\n    return NaN;\n}\nfunction toUtcDate(date) {\n    return (0,_newDateUTC_index_js__WEBPACK_IMPORTED_MODULE_1__.newDateUTC)(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n}\nfunction calcOffset(date, timezoneString) {\n    const tokens = (0,_tzTokenizeDate_index_js__WEBPACK_IMPORTED_MODULE_0__.tzTokenizeDate)(date, timezoneString);\n    // ms dropped because it's not provided by tzTokenizeDate\n    const asUTC = (0,_newDateUTC_index_js__WEBPACK_IMPORTED_MODULE_1__.newDateUTC)(tokens[0], tokens[1] - 1, tokens[2], tokens[3] % 24, tokens[4], tokens[5], 0).getTime();\n    let asTS = date.getTime();\n    const over = asTS % 1000;\n    asTS -= over >= 0 ? over : 1000 + over;\n    return asUTC - asTS;\n}\nfunction fixOffset(date, offset, timezoneString) {\n    const localTS = date.getTime();\n    // Our UTC time is just a guess because our offset is just a guess\n    let utcGuess = localTS - offset;\n    // Test whether the zone matches the offset for this ts\n    const o2 = calcOffset(new Date(utcGuess), timezoneString);\n    // If so, offset didn't change, and we're done\n    if (offset === o2) {\n        return offset;\n    }\n    // If not, change the ts by the difference in the offset\n    utcGuess -= o2 - offset;\n    // If that gives us the local time we want, we're done\n    const o3 = calcOffset(new Date(utcGuess), timezoneString);\n    if (o2 === o3) {\n        return o2;\n    }\n    // If it's different, we're in a hole time. The offset has changed, but we don't adjust the time\n    return Math.max(o2, o3);\n}\nfunction validateTimezone(hours, minutes) {\n    return -23 <= hours && hours <= 23 && (minutes == null || (0 <= minutes && minutes <= 59));\n}\nconst validIANATimezoneCache = {};\nfunction isValidTimezoneIANAString(timeZoneString) {\n    if (validIANATimezoneCache[timeZoneString])\n        return true;\n    try {\n        new Intl.DateTimeFormat(undefined, { timeZone: timeZoneString });\n        validIANATimezoneCache[timeZoneString] = true;\n        return true;\n    }\n    catch (error) {\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/tzParseTimezone/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/tzPattern/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/date-fns-tz/dist/esm/_lib/tzPattern/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tzPattern: () => (/* binding */ tzPattern)\n/* harmony export */ });\n/** Regex to identify the presence of a time zone specifier in a date string */\nconst tzPattern = /(Z|[+-]\\d{2}(?::?\\d{2})?| UTC| [a-zA-Z]+\\/[a-zA-Z_]+(?:\\/[a-zA-Z_]+)?)$/;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZGF0ZS1mbnMtdHovZGlzdC9lc20vX2xpYi90elBhdHRlcm4vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ08sNkJBQTZCLEVBQUUsUUFBUSxFQUFFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJveGFzXFxPbmVEcml2ZVxcRGVza3RvcFxcUFJPSkVDVFNcXHR1Y3NvbmxvdmVzbXVzaWMuY29tXFxQcm90b3R5cGUxM1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZGF0ZS1mbnMtdHpcXGRpc3RcXGVzbVxcX2xpYlxcdHpQYXR0ZXJuXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiogUmVnZXggdG8gaWRlbnRpZnkgdGhlIHByZXNlbmNlIG9mIGEgdGltZSB6b25lIHNwZWNpZmllciBpbiBhIGRhdGUgc3RyaW5nICovXG5leHBvcnQgY29uc3QgdHpQYXR0ZXJuID0gLyhafFsrLV1cXGR7Mn0oPzo6P1xcZHsyfSk/fCBVVEN8IFthLXpBLVpdK1xcL1thLXpBLVpfXSsoPzpcXC9bYS16QS1aX10rKT8pJC87XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/tzPattern/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/tzTokenizeDate/index.js":
/*!************************************************************************!*\
  !*** ./node_modules/date-fns-tz/dist/esm/_lib/tzTokenizeDate/index.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tzTokenizeDate: () => (/* binding */ tzTokenizeDate)\n/* harmony export */ });\n/**\n * Returns the [year, month, day, hour, minute, seconds] tokens of the provided\n * `date` as it will be rendered in the `timeZone`.\n */\nfunction tzTokenizeDate(date, timeZone) {\n    const dtf = getDateTimeFormat(timeZone);\n    return 'formatToParts' in dtf ? partsOffset(dtf, date) : hackyOffset(dtf, date);\n}\nconst typeToPos = {\n    year: 0,\n    month: 1,\n    day: 2,\n    hour: 3,\n    minute: 4,\n    second: 5,\n};\nfunction partsOffset(dtf, date) {\n    try {\n        const formatted = dtf.formatToParts(date);\n        const filled = [];\n        for (let i = 0; i < formatted.length; i++) {\n            const pos = typeToPos[formatted[i].type];\n            if (pos !== undefined) {\n                filled[pos] = parseInt(formatted[i].value, 10);\n            }\n        }\n        return filled;\n    }\n    catch (error) {\n        if (error instanceof RangeError) {\n            return [NaN];\n        }\n        throw error;\n    }\n}\nfunction hackyOffset(dtf, date) {\n    const formatted = dtf.format(date);\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const parsed = /(\\d+)\\/(\\d+)\\/(\\d+),? (\\d+):(\\d+):(\\d+)/.exec(formatted);\n    // const [, fMonth, fDay, fYear, fHour, fMinute, fSecond] = parsed\n    // return [fYear, fMonth, fDay, fHour, fMinute, fSecond]\n    return [\n        parseInt(parsed[3], 10),\n        parseInt(parsed[1], 10),\n        parseInt(parsed[2], 10),\n        parseInt(parsed[4], 10),\n        parseInt(parsed[5], 10),\n        parseInt(parsed[6], 10),\n    ];\n}\n// Get a cached Intl.DateTimeFormat instance for the IANA `timeZone`. This can be used\n// to get deterministic local date/time output according to the `en-US` locale which\n// can be used to extract local time parts as necessary.\nconst dtfCache = {};\n// New browsers use `hourCycle`, IE and Chrome <73 does not support it and uses `hour12`\nconst testDateFormatted = new Intl.DateTimeFormat('en-US', {\n    hourCycle: 'h23',\n    timeZone: 'America/New_York',\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n}).format(new Date('2014-06-25T04:00:00.123Z'));\nconst hourCycleSupported = testDateFormatted === '06/25/2014, 00:00:00' ||\n    testDateFormatted === '‎06‎/‎25‎/‎2014‎ ‎00‎:‎00‎:‎00';\nfunction getDateTimeFormat(timeZone) {\n    if (!dtfCache[timeZone]) {\n        dtfCache[timeZone] = hourCycleSupported\n            ? new Intl.DateTimeFormat('en-US', {\n                hourCycle: 'h23',\n                timeZone: timeZone,\n                year: 'numeric',\n                month: 'numeric',\n                day: '2-digit',\n                hour: '2-digit',\n                minute: '2-digit',\n                second: '2-digit',\n            })\n            : new Intl.DateTimeFormat('en-US', {\n                hour12: false,\n                timeZone: timeZone,\n                year: 'numeric',\n                month: 'numeric',\n                day: '2-digit',\n                hour: '2-digit',\n                minute: '2-digit',\n                second: '2-digit',\n            });\n    }\n    return dtfCache[timeZone];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/tzTokenizeDate/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/date-fns-tz/dist/esm/format/formatters/index.js":
/*!**********************************************************************!*\
  !*** ./node_modules/date-fns-tz/dist/esm/format/formatters/index.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatters: () => (/* binding */ formatters)\n/* harmony export */ });\n/* harmony import */ var _lib_tzIntlTimeZoneName_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/tzIntlTimeZoneName/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/tzIntlTimeZoneName/index.js\");\n/* harmony import */ var _lib_tzParseTimezone_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/tzParseTimezone/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/tzParseTimezone/index.js\");\n\n\nconst MILLISECONDS_IN_MINUTE = 60 * 1000;\nconst formatters = {\n    // Timezone (ISO-8601. If offset is 0, output is always `'Z'`)\n    X: function (date, token, options) {\n        const timezoneOffset = getTimeZoneOffset(options.timeZone, date);\n        if (timezoneOffset === 0) {\n            return 'Z';\n        }\n        switch (token) {\n            // Hours and optional minutes\n            case 'X':\n                return formatTimezoneWithOptionalMinutes(timezoneOffset);\n            // Hours, minutes and optional seconds without `:` delimeter\n            // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n            // so this token always has the same output as `XX`\n            case 'XXXX':\n            case 'XX': // Hours and minutes without `:` delimeter\n                return formatTimezone(timezoneOffset);\n            // Hours, minutes and optional seconds with `:` delimeter\n            // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n            // so this token always has the same output as `XXX`\n            case 'XXXXX':\n            case 'XXX': // Hours and minutes with `:` delimeter\n            default:\n                return formatTimezone(timezoneOffset, ':');\n        }\n    },\n    // Timezone (ISO-8601. If offset is 0, output is `'+00:00'` or equivalent)\n    x: function (date, token, options) {\n        const timezoneOffset = getTimeZoneOffset(options.timeZone, date);\n        switch (token) {\n            // Hours and optional minutes\n            case 'x':\n                return formatTimezoneWithOptionalMinutes(timezoneOffset);\n            // Hours, minutes and optional seconds without `:` delimeter\n            // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n            // so this token always has the same output as `xx`\n            case 'xxxx':\n            case 'xx': // Hours and minutes without `:` delimeter\n                return formatTimezone(timezoneOffset);\n            // Hours, minutes and optional seconds with `:` delimeter\n            // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n            // so this token always has the same output as `xxx`\n            case 'xxxxx':\n            case 'xxx': // Hours and minutes with `:` delimeter\n            default:\n                return formatTimezone(timezoneOffset, ':');\n        }\n    },\n    // Timezone (GMT)\n    O: function (date, token, options) {\n        const timezoneOffset = getTimeZoneOffset(options.timeZone, date);\n        switch (token) {\n            // Short\n            case 'O':\n            case 'OO':\n            case 'OOO':\n                return 'GMT' + formatTimezoneShort(timezoneOffset, ':');\n            // Long\n            case 'OOOO':\n            default:\n                return 'GMT' + formatTimezone(timezoneOffset, ':');\n        }\n    },\n    // Timezone (specific non-location)\n    z: function (date, token, options) {\n        switch (token) {\n            // Short\n            case 'z':\n            case 'zz':\n            case 'zzz':\n                return (0,_lib_tzIntlTimeZoneName_index_js__WEBPACK_IMPORTED_MODULE_0__.tzIntlTimeZoneName)('short', date, options);\n            // Long\n            case 'zzzz':\n            default:\n                return (0,_lib_tzIntlTimeZoneName_index_js__WEBPACK_IMPORTED_MODULE_0__.tzIntlTimeZoneName)('long', date, options);\n        }\n    },\n};\nfunction getTimeZoneOffset(timeZone, originalDate) {\n    const timeZoneOffset = timeZone\n        ? (0,_lib_tzParseTimezone_index_js__WEBPACK_IMPORTED_MODULE_1__.tzParseTimezone)(timeZone, originalDate, true) / MILLISECONDS_IN_MINUTE\n        : originalDate?.getTimezoneOffset() ?? 0;\n    if (Number.isNaN(timeZoneOffset)) {\n        throw new RangeError('Invalid time zone specified: ' + timeZone);\n    }\n    return timeZoneOffset;\n}\nfunction addLeadingZeros(number, targetLength) {\n    const sign = number < 0 ? '-' : '';\n    let output = Math.abs(number).toString();\n    while (output.length < targetLength) {\n        output = '0' + output;\n    }\n    return sign + output;\n}\nfunction formatTimezone(offset, delimiter = '') {\n    const sign = offset > 0 ? '-' : '+';\n    const absOffset = Math.abs(offset);\n    const hours = addLeadingZeros(Math.floor(absOffset / 60), 2);\n    const minutes = addLeadingZeros(Math.floor(absOffset % 60), 2);\n    return sign + hours + delimiter + minutes;\n}\nfunction formatTimezoneWithOptionalMinutes(offset, delimiter) {\n    if (offset % 60 === 0) {\n        const sign = offset > 0 ? '-' : '+';\n        return sign + addLeadingZeros(Math.abs(offset) / 60, 2);\n    }\n    return formatTimezone(offset, delimiter);\n}\nfunction formatTimezoneShort(offset, delimiter = '') {\n    const sign = offset > 0 ? '-' : '+';\n    const absOffset = Math.abs(offset);\n    const hours = Math.floor(absOffset / 60);\n    const minutes = absOffset % 60;\n    if (minutes === 0) {\n        return sign + String(hours);\n    }\n    return sign + String(hours) + delimiter + addLeadingZeros(minutes, 2);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/date-fns-tz/dist/esm/format/formatters/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/date-fns-tz/dist/esm/format/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/date-fns-tz/dist/esm/format/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   format: () => (/* binding */ format)\n/* harmony export */ });\n/* harmony import */ var date_fns_format__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! date-fns/format */ \"(ssr)/./node_modules/date-fns/format.mjs\");\n/* harmony import */ var _formatters_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatters/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/format/formatters/index.js\");\n/* harmony import */ var _toDate_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../toDate/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/toDate/index.js\");\n\n\n\nconst tzFormattingTokensRegExp = /([xXOz]+)|''|'(''|[^'])+('|$)/g;\n/**\n * @name format\n * @category Common Helpers\n * @summary Format the date.\n *\n * @description\n * Return the formatted date string in the given format. The result may consty by locale.\n *\n * > ⚠️ Please note that the `format` tokens differ from Moment.js and other libraries.\n * > See: https://git.io/fxCyr\n *\n * The characters wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n * (see the last example)\n *\n * Format of the string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * with a few additions (see note 7 below the table).\n *\n * Accepted patterns:\n * | Unit                            | Pattern | Result examples                   | Notes |\n * |---------------------------------|---------|-----------------------------------|-------|\n * | Era                             | G..GGG  | AD, BC                            |       |\n * |                                 | GGGG    | Anno Domini, Before Christ        | 2     |\n * |                                 | GGGGG   | A, B                              |       |\n * | Calendar year                   | y       | 44, 1, 1900, 2017                 | 5     |\n * |                                 | yo      | 44th, 1st, 0th, 17th              | 5,7   |\n * |                                 | yy      | 44, 01, 00, 17                    | 5     |\n * |                                 | yyy     | 044, 001, 1900, 2017              | 5     |\n * |                                 | yyyy    | 0044, 0001, 1900, 2017            | 5     |\n * |                                 | yyyyy   | ...                               | 3,5   |\n * | Local week-numbering year       | Y       | 44, 1, 1900, 2017                 | 5     |\n * |                                 | Yo      | 44th, 1st, 1900th, 2017th         | 5,7   |\n * |                                 | YY      | 44, 01, 00, 17                    | 5,8   |\n * |                                 | YYY     | 044, 001, 1900, 2017              | 5     |\n * |                                 | YYYY    | 0044, 0001, 1900, 2017            | 5,8   |\n * |                                 | YYYYY   | ...                               | 3,5   |\n * | ISO week-numbering year         | R       | -43, 0, 1, 1900, 2017             | 5,7   |\n * |                                 | RR      | -43, 00, 01, 1900, 2017           | 5,7   |\n * |                                 | RRR     | -043, 000, 001, 1900, 2017        | 5,7   |\n * |                                 | RRRR    | -0043, 0000, 0001, 1900, 2017     | 5,7   |\n * |                                 | RRRRR   | ...                               | 3,5,7 |\n * | Extended year                   | u       | -43, 0, 1, 1900, 2017             | 5     |\n * |                                 | uu      | -43, 01, 1900, 2017               | 5     |\n * |                                 | uuu     | -043, 001, 1900, 2017             | 5     |\n * |                                 | uuuu    | -0043, 0001, 1900, 2017           | 5     |\n * |                                 | uuuuu   | ...                               | 3,5   |\n * | Quarter (formatting)            | Q       | 1, 2, 3, 4                        |       |\n * |                                 | Qo      | 1st, 2nd, 3rd, 4th                | 7     |\n * |                                 | QQ      | 01, 02, 03, 04                    |       |\n * |                                 | QQQ     | Q1, Q2, Q3, Q4                    |       |\n * |                                 | QQQQ    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 | QQQQQ   | 1, 2, 3, 4                        | 4     |\n * | Quarter (stand-alone)           | q       | 1, 2, 3, 4                        |       |\n * |                                 | qo      | 1st, 2nd, 3rd, 4th                | 7     |\n * |                                 | qq      | 01, 02, 03, 04                    |       |\n * |                                 | qqq     | Q1, Q2, Q3, Q4                    |       |\n * |                                 | qqqq    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 | qqqqq   | 1, 2, 3, 4                        | 4     |\n * | Month (formatting)              | M       | 1, 2, ..., 12                     |       |\n * |                                 | Mo      | 1st, 2nd, ..., 12th               | 7     |\n * |                                 | MM      | 01, 02, ..., 12                   |       |\n * |                                 | MMM     | Jan, Feb, ..., Dec                |       |\n * |                                 | MMMM    | January, February, ..., December  | 2     |\n * |                                 | MMMMM   | J, F, ..., D                      |       |\n * | Month (stand-alone)             | L       | 1, 2, ..., 12                     |       |\n * |                                 | Lo      | 1st, 2nd, ..., 12th               | 7     |\n * |                                 | LL      | 01, 02, ..., 12                   |       |\n * |                                 | LLL     | Jan, Feb, ..., Dec                |       |\n * |                                 | LLLL    | January, February, ..., December  | 2     |\n * |                                 | LLLLL   | J, F, ..., D                      |       |\n * | Local week of year              | w       | 1, 2, ..., 53                     |       |\n * |                                 | wo      | 1st, 2nd, ..., 53th               | 7     |\n * |                                 | ww      | 01, 02, ..., 53                   |       |\n * | ISO week of year                | I       | 1, 2, ..., 53                     | 7     |\n * |                                 | Io      | 1st, 2nd, ..., 53th               | 7     |\n * |                                 | II      | 01, 02, ..., 53                   | 7     |\n * | Day of month                    | d       | 1, 2, ..., 31                     |       |\n * |                                 | do      | 1st, 2nd, ..., 31st               | 7     |\n * |                                 | dd      | 01, 02, ..., 31                   |       |\n * | Day of year                     | D       | 1, 2, ..., 365, 366               | 8     |\n * |                                 | Do      | 1st, 2nd, ..., 365th, 366th       | 7     |\n * |                                 | DD      | 01, 02, ..., 365, 366             | 8     |\n * |                                 | DDD     | 001, 002, ..., 365, 366           |       |\n * |                                 | DDDD    | ...                               | 3     |\n * | Day of week (formatting)        | E..EEE  | Mon, Tue, Wed, ..., Su            |       |\n * |                                 | EEEE    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 | EEEEE   | M, T, W, T, F, S, S               |       |\n * |                                 | EEEEEE  | Mo, Tu, We, Th, Fr, Su, Sa        |       |\n * | ISO day of week (formatting)    | i       | 1, 2, 3, ..., 7                   | 7     |\n * |                                 | io      | 1st, 2nd, ..., 7th                | 7     |\n * |                                 | ii      | 01, 02, ..., 07                   | 7     |\n * |                                 | iii     | Mon, Tue, Wed, ..., Su            | 7     |\n * |                                 | iiii    | Monday, Tuesday, ..., Sunday      | 2,7   |\n * |                                 | iiiii   | M, T, W, T, F, S, S               | 7     |\n * |                                 | iiiiii  | Mo, Tu, We, Th, Fr, Su, Sa        | 7     |\n * | Local day of week (formatting)  | e       | 2, 3, 4, ..., 1                   |       |\n * |                                 | eo      | 2nd, 3rd, ..., 1st                | 7     |\n * |                                 | ee      | 02, 03, ..., 01                   |       |\n * |                                 | eee     | Mon, Tue, Wed, ..., Su            |       |\n * |                                 | eeee    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 | eeeee   | M, T, W, T, F, S, S               |       |\n * |                                 | eeeeee  | Mo, Tu, We, Th, Fr, Su, Sa        |       |\n * | Local day of week (stand-alone) | c       | 2, 3, 4, ..., 1                   |       |\n * |                                 | co      | 2nd, 3rd, ..., 1st                | 7     |\n * |                                 | cc      | 02, 03, ..., 01                   |       |\n * |                                 | ccc     | Mon, Tue, Wed, ..., Su            |       |\n * |                                 | cccc    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 | ccccc   | M, T, W, T, F, S, S               |       |\n * |                                 | cccccc  | Mo, Tu, We, Th, Fr, Su, Sa        |       |\n * | AM, PM                          | a..aaa  | AM, PM                            |       |\n * |                                 | aaaa    | a.m., p.m.                        | 2     |\n * |                                 | aaaaa   | a, p                              |       |\n * | AM, PM, noon, midnight          | b..bbb  | AM, PM, noon, midnight            |       |\n * |                                 | bbbb    | a.m., p.m., noon, midnight        | 2     |\n * |                                 | bbbbb   | a, p, n, mi                       |       |\n * | Flexible day period             | B..BBB  | at night, in the morning, ...     |       |\n * |                                 | BBBB    | at night, in the morning, ...     | 2     |\n * |                                 | BBBBB   | at night, in the morning, ...     |       |\n * | Hour [1-12]                     | h       | 1, 2, ..., 11, 12                 |       |\n * |                                 | ho      | 1st, 2nd, ..., 11th, 12th         | 7     |\n * |                                 | hh      | 01, 02, ..., 11, 12               |       |\n * | Hour [0-23]                     | H       | 0, 1, 2, ..., 23                  |       |\n * |                                 | Ho      | 0th, 1st, 2nd, ..., 23rd          | 7     |\n * |                                 | HH      | 00, 01, 02, ..., 23               |       |\n * | Hour [0-11]                     | K       | 1, 2, ..., 11, 0                  |       |\n * |                                 | Ko      | 1st, 2nd, ..., 11th, 0th          | 7     |\n * |                                 | KK      | 1, 2, ..., 11, 0                  |       |\n * | Hour [1-24]                     | k       | 24, 1, 2, ..., 23                 |       |\n * |                                 | ko      | 24th, 1st, 2nd, ..., 23rd         | 7     |\n * |                                 | kk      | 24, 01, 02, ..., 23               |       |\n * | Minute                          | m       | 0, 1, ..., 59                     |       |\n * |                                 | mo      | 0th, 1st, ..., 59th               | 7     |\n * |                                 | mm      | 00, 01, ..., 59                   |       |\n * | Second                          | s       | 0, 1, ..., 59                     |       |\n * |                                 | so      | 0th, 1st, ..., 59th               | 7     |\n * |                                 | ss      | 00, 01, ..., 59                   |       |\n * | Fraction of second              | S       | 0, 1, ..., 9                      |       |\n * |                                 | SS      | 00, 01, ..., 99                   |       |\n * |                                 | SSS     | 000, 0001, ..., 999               |       |\n * |                                 | SSSS    | ...                               | 3     |\n * | Timezone (ISO-8601 w/ Z)        | X       | -08, +0530, Z                     |       |\n * |                                 | XX      | -0800, +0530, Z                   |       |\n * |                                 | XXX     | -08:00, +05:30, Z                 |       |\n * |                                 | XXXX    | -0800, +0530, Z, +123456          | 2     |\n * |                                 | XXXXX   | -08:00, +05:30, Z, +12:34:56      |       |\n * | Timezone (ISO-8601 w/o Z)       | x       | -08, +0530, +00                   |       |\n * |                                 | xx      | -0800, +0530, +0000               |       |\n * |                                 | xxx     | -08:00, +05:30, +00:00            | 2     |\n * |                                 | xxxx    | -0800, +0530, +0000, +123456      |       |\n * |                                 | xxxxx   | -08:00, +05:30, +00:00, +12:34:56 |       |\n * | Timezone (GMT)                  | O...OOO | GMT-8, GMT+5:30, GMT+0            |       |\n * |                                 | OOOO    | GMT-08:00, GMT+05:30, GMT+00:00   | 2     |\n * | Timezone (specific non-locat.)  | z...zzz | PDT, EST, CEST                    | 6     |\n * |                                 | zzzz    | Pacific Daylight Time             | 2,6   |\n * | Seconds timestamp               | t       | 512969520                         | 7     |\n * |                                 | tt      | ...                               | 3,7   |\n * | Milliseconds timestamp          | T       | 512969520900                      | 7     |\n * |                                 | TT      | ...                               | 3,7   |\n * | Long localized date             | P       | 05/29/1453                        | 7     |\n * |                                 | PP      | May 29, 1453                      | 7     |\n * |                                 | PPP     | May 29th, 1453                    | 7     |\n * |                                 | PPPP    | Sunday, May 29th, 1453            | 2,7   |\n * | Long localized time             | p       | 12:00 AM                          | 7     |\n * |                                 | pp      | 12:00:00 AM                       | 7     |\n * |                                 | ppp     | 12:00:00 AM GMT+2                 | 7     |\n * |                                 | pppp    | 12:00:00 AM GMT+02:00             | 2,7   |\n * | Combination of date and time    | Pp      | 05/29/1453, 12:00 AM              | 7     |\n * |                                 | PPpp    | May 29, 1453, 12:00:00 AM         | 7     |\n * |                                 | PPPppp  | May 29th, 1453 at ...             | 7     |\n * |                                 | PPPPpppp| Sunday, May 29th, 1453 at ...     | 2,7   |\n * Notes:\n * 1. \"Formatting\" units (e.g. formatting quarter) in the default en-US locale\n *    are the same as \"stand-alone\" units, but are different in some languages.\n *    \"Formatting\" units are declined according to the rules of the language\n *    in the context of a date. \"Stand-alone\" units are always nominative singular:\n *\n *    `format(new Date(2017, 10, 6), 'do LLLL', {locale: cs}) //=> '6. listopad'`\n *\n *    `format(new Date(2017, 10, 6), 'do MMMM', {locale: cs}) //=> '6. listopadu'`\n *\n * 2. Any sequence of the identical letters is a pattern, unless it is escaped by\n *    the single quote characters (see below).\n *    If the sequence is longer than listed in table (e.g. `EEEEEEEEEEE`)\n *    the output will be the same as default pattern for this unit, usually\n *    the longest one (in case of ISO weekdays, `EEEE`). Default patterns for units\n *    are marked with \"2\" in the last column of the table.\n *\n *    `format(new Date(2017, 10, 6), 'MMM') //=> 'Nov'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMM') //=> 'November'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMMM') //=> 'N'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMMMM') //=> 'November'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMMMMM') //=> 'November'`\n *\n * 3. Some patterns could be unlimited length (such as `yyyyyyyy`).\n *    The output will be padded with zeros to match the length of the pattern.\n *\n *    `format(new Date(2017, 10, 6), 'yyyyyyyy') //=> '00002017'`\n *\n * 4. `QQQQQ` and `qqqqq` could be not strictly numerical in some locales.\n *    These tokens represent the shortest form of the quarter.\n *\n * 5. The main difference between `y` and `u` patterns are B.C. years:\n *\n *    | Year | `y` | `u` |\n *    |------|-----|-----|\n *    | AC 1 |   1 |   1 |\n *    | BC 1 |   1 |   0 |\n *    | BC 2 |   2 |  -1 |\n *\n *    Also `yy` always returns the last two digits of a year,\n *    while `uu` pads single digit years to 2 characters and returns other years unchanged:\n *\n *    | Year | `yy` | `uu` |\n *    |------|------|------|\n *    | 1    |   01 |   01 |\n *    | 14   |   14 |   14 |\n *    | 376  |   76 |  376 |\n *    | 1453 |   53 | 1453 |\n *\n *    The same difference is true for local and ISO week-numbering years (`Y` and `R`),\n *    except local week-numbering years are dependent on `options.weekStartsOn`\n *    and `options.firstWeekContainsDate` (compare [getISOWeekYear]{@link https://date-fns.org/docs/getISOWeekYear}\n *    and [getWeekYear]{@link https://date-fns.org/docs/getWeekYear}).\n *\n * 6. Specific non-location timezones are created using the Intl browser API. The output is determined by the\n *    preferred standard of the current locale (en-US by default) which may not always give the expected result.\n *    For this reason it is recommended to supply a `locale` in the format options when formatting a time zone name.\n *\n * 7. These patterns are not in the Unicode Technical Standard #35:\n *    - `i`: ISO day of week\n *    - `I`: ISO week of year\n *    - `R`: ISO week-numbering year\n *    - `t`: seconds timestamp\n *    - `T`: milliseconds timestamp\n *    - `o`: ordinal number modifier\n *    - `P`: long localized date\n *    - `p`: long localized time\n *\n * 8. These tokens are often confused with others. See: https://git.io/fxCyr\n *\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole\n *   library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * - The second argument is now required for the sake of explicitness.\n *\n *   ```javascript\n *   // Before v2.0.0\n *   format(new Date(2016, 0, 1))\n *\n *   // v2.0.0 onward\n *   format(new Date(2016, 0, 1), \"yyyy-MM-dd'T'HH:mm:ss.SSSxxx\")\n *   ```\n *\n * - New format string API for `format` function\n *   which is based on [Unicode Technical Standard\n *   #35](https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table). See [this\n *   post](https://blog.date-fns.org/post/unicode-tokens-in-date-fns-v2-sreatyki91jg) for more details.\n *\n * - Characters are now escaped using single quote symbols (`'`) instead of square brackets.\n *\n * @param date the original date\n * @param formatStr the string of tokens\n * @param options the object with options. See [Options]{@link https://date-fns.org/docs/Options}\n * @param {0|1|2} [options.additionalDigits=2] - passed to `toDate`. See [toDate]{@link\n *   https://date-fns.org/docs/toDate}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @param {Number} [options.firstWeekContainsDate=1] - the day of January, which is\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See\n *   [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {Boolean} [options.awareOfUnicodeTokens=false] - if true, allows usage of Unicode tokens causes confusion:\n *   - Some of the day of year tokens (`D`, `DD`) that are confused with the day of month tokens (`d`, `dd`).\n *   - Some of the local week-numbering year tokens (`YY`, `YYYY`) that are confused with the calendar year tokens\n *   (`yy`, `yyyy`). See: https://git.io/fxCyr\n * @param {String} [options.timeZone=''] - used to specify the IANA time zone offset of a date String.\n * @param {Date|Number} [options.originalDate] - can be used to pass the original unmodified date to `format` to\n *   improve correctness of the replaced timezone token close to the DST threshold.\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.additionalDigits` must be 0, 1 or 2\n * @throws {RangeError} `options.locale` must contain `localize` property\n * @throws {RangeError} `options.locale` must contain `formatLong` property\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.firstWeekContainsDate` must be between 1 and 7\n * @throws {RangeError} `options.awareOfUnicodeTokens` must be set to `true` to use `XX` token; see:\n *   https://git.io/fxCyr\n *\n * @example\n * // Represent 11 February 2014 in middle-endian format:\n * const result = format(new Date(2014, 1, 11), 'MM/dd/yyyy')\n * //=> '02/11/2014'\n *\n * @example\n * // Represent 2 July 2014 in Esperanto:\n * import { eoLocale } from 'date-fns/locale/eo'\n * const result = format(new Date(2014, 6, 2), \"do 'de' MMMM yyyy\", {\n *   locale: eoLocale\n * })\n * //=> '2-a de julio 2014'\n *\n * @example\n * // Escape string by single quote characters:\n * const result = format(new Date(2014, 6, 2, 15), \"h 'o''clock'\")\n * //=> \"3 o'clock\"\n */\nfunction format(date, formatStr, options = {}) {\n    formatStr = String(formatStr);\n    const matches = formatStr.match(tzFormattingTokensRegExp);\n    if (matches) {\n        const d = (0,_toDate_index_js__WEBPACK_IMPORTED_MODULE_1__.toDate)(options.originalDate || date, options);\n        // Work through each match and replace the tz token in the format string with the quoted\n        // formatted time zone so the remaining tokens can be filled in by date-fns#format.\n        formatStr = matches.reduce(function (result, token) {\n            if (token[0] === \"'\") {\n                return result; // This is a quoted portion, matched only to ensure we don't match inside it\n            }\n            const pos = result.indexOf(token);\n            const precededByQuotedSection = result[pos - 1] === \"'\";\n            const replaced = result.replace(token, \"'\" + _formatters_index_js__WEBPACK_IMPORTED_MODULE_0__.formatters[token[0]](d, token, options) + \"'\");\n            // If the replacement results in two adjoining quoted strings, the back to back quotes\n            // are removed, so it doesn't look like an escaped quote.\n            return precededByQuotedSection\n                ? replaced.substring(0, pos - 1) + replaced.substring(pos + 1)\n                : replaced;\n        }, formatStr);\n    }\n    return (0,date_fns_format__WEBPACK_IMPORTED_MODULE_2__.format)(date, formatStr, options);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/date-fns-tz/dist/esm/format/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/date-fns-tz/dist/esm/formatInTimeZone/index.js":
/*!*********************************************************************!*\
  !*** ./node_modules/date-fns-tz/dist/esm/formatInTimeZone/index.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatInTimeZone: () => (/* binding */ formatInTimeZone)\n/* harmony export */ });\n/* harmony import */ var _format_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../format/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/format/index.js\");\n/* harmony import */ var _toZonedTime_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../toZonedTime/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/toZonedTime/index.js\");\n\n\n/**\n * @name formatInTimeZone\n * @category Time Zone Helpers\n * @summary Gets the offset in milliseconds between the time zone and Universal Coordinated Time (UTC)\n *\n * @param date the date representing the local time / real UTC time\n * @param timeZone the time zone this date should be formatted for; can be an offset or IANA time zone\n * @param formatStr the string of tokens\n * @param options the object with options. See [Options]{@link https://date-fns.org/docs/Options}\n * @param {0|1|2} [options.additionalDigits=2] - passed to `toDate`. See [toDate]{@link\n *   https://date-fns.org/docs/toDate}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @param {Number} [options.firstWeekContainsDate=1] - the day of January, which is\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See\n *   [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {Boolean} [options.awareOfUnicodeTokens=false] - if true, allows usage of Unicode tokens causes confusion:\n *   - Some of the day of year tokens (`D`, `DD`) that are confused with the day of month tokens (`d`, `dd`).\n *   - Some of the local week-numbering year tokens (`YY`, `YYYY`) that are confused with the calendar year tokens\n *   (`yy`, `yyyy`). See: https://git.io/fxCyr\n * @param {String} [options.timeZone=''] - used to specify the IANA time zone offset of a date String.\n */\nfunction formatInTimeZone(date, timeZone, formatStr, options) {\n    options = {\n        ...options,\n        timeZone,\n        originalDate: date,\n    };\n    return (0,_format_index_js__WEBPACK_IMPORTED_MODULE_0__.format)((0,_toZonedTime_index_js__WEBPACK_IMPORTED_MODULE_1__.toZonedTime)(date, timeZone, { timeZone: options.timeZone }), formatStr, options);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/date-fns-tz/dist/esm/formatInTimeZone/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/date-fns-tz/dist/esm/fromZonedTime/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/date-fns-tz/dist/esm/fromZonedTime/index.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromZonedTime: () => (/* binding */ fromZonedTime)\n/* harmony export */ });\n/* harmony import */ var _toDate_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../toDate/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/toDate/index.js\");\n/* harmony import */ var _lib_tzPattern_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../_lib/tzPattern/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/tzPattern/index.js\");\n/* harmony import */ var _lib_tzParseTimezone_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_lib/tzParseTimezone/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/tzParseTimezone/index.js\");\n/* harmony import */ var _lib_newDateUTC_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../_lib/newDateUTC/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/newDateUTC/index.js\");\n\n\n\n\n/**\n * @name fromZonedTime\n * @category Time Zone Helpers\n * @summary Get the UTC date/time from a date representing local time in a given time zone\n *\n * @description\n * Returns a date instance with the UTC time of the provided date of which the values\n * represented the local time in the time zone specified. In other words, if the input\n * date represented local time in time zone, the timestamp of the output date will\n * give the equivalent UTC of that local time regardless of the current system time zone.\n *\n * @param date the date with values representing the local time\n * @param timeZone the time zone of this local time, can be an offset or IANA time zone\n * @param options the object with options. See [Options]{@link https://date-fns.org/docs/Options}\n * @param {0|1|2} [options.additionalDigits=2] - passed to `toDate`. See [toDate]{@link https://date-fns.org/docs/toDate}\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.additionalDigits` must be 0, 1 or 2\n *\n * @example\n * // In June 10am in Los Angeles is 5pm UTC\n * const result = fromZonedTime(new Date(2014, 5, 25, 10, 0, 0), 'America/Los_Angeles')\n * //=> 2014-06-25T17:00:00.000Z\n */\nfunction fromZonedTime(date, timeZone, options) {\n    if (typeof date === 'string' && !date.match(_lib_tzPattern_index_js__WEBPACK_IMPORTED_MODULE_1__.tzPattern)) {\n        return (0,_toDate_index_js__WEBPACK_IMPORTED_MODULE_0__.toDate)(date, { ...options, timeZone });\n    }\n    date = (0,_toDate_index_js__WEBPACK_IMPORTED_MODULE_0__.toDate)(date, options);\n    const utc = (0,_lib_newDateUTC_index_js__WEBPACK_IMPORTED_MODULE_3__.newDateUTC)(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds()).getTime();\n    const offsetMilliseconds = (0,_lib_tzParseTimezone_index_js__WEBPACK_IMPORTED_MODULE_2__.tzParseTimezone)(timeZone, new Date(utc));\n    return new Date(utc + offsetMilliseconds);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZGF0ZS1mbnMtdHovZGlzdC9lc20vZnJvbVpvbmVkVGltZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUE0QztBQUNXO0FBQ1k7QUFDVjtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlEQUF5RDtBQUN6RCxXQUFXLE9BQU8sZ0VBQWdFO0FBQ2xGLFlBQVksV0FBVztBQUN2QixZQUFZLFlBQVk7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxnREFBZ0QsOERBQVM7QUFDekQsZUFBZSx3REFBTSxTQUFTLHNCQUFzQjtBQUNwRDtBQUNBLFdBQVcsd0RBQU07QUFDakIsZ0JBQWdCLG9FQUFVO0FBQzFCLCtCQUErQiw4RUFBZTtBQUM5QztBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJveGFzXFxPbmVEcml2ZVxcRGVza3RvcFxcUFJPSkVDVFNcXHR1Y3NvbmxvdmVzbXVzaWMuY29tXFxQcm90b3R5cGUxM1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZGF0ZS1mbnMtdHpcXGRpc3RcXGVzbVxcZnJvbVpvbmVkVGltZVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdG9EYXRlIH0gZnJvbSAnLi4vdG9EYXRlL2luZGV4LmpzJztcbmltcG9ydCB7IHR6UGF0dGVybiB9IGZyb20gJy4uL19saWIvdHpQYXR0ZXJuL2luZGV4LmpzJztcbmltcG9ydCB7IHR6UGFyc2VUaW1lem9uZSB9IGZyb20gJy4uL19saWIvdHpQYXJzZVRpbWV6b25lL2luZGV4LmpzJztcbmltcG9ydCB7IG5ld0RhdGVVVEMgfSBmcm9tICcuLi9fbGliL25ld0RhdGVVVEMvaW5kZXguanMnO1xuLyoqXG4gKiBAbmFtZSBmcm9tWm9uZWRUaW1lXG4gKiBAY2F0ZWdvcnkgVGltZSBab25lIEhlbHBlcnNcbiAqIEBzdW1tYXJ5IEdldCB0aGUgVVRDIGRhdGUvdGltZSBmcm9tIGEgZGF0ZSByZXByZXNlbnRpbmcgbG9jYWwgdGltZSBpbiBhIGdpdmVuIHRpbWUgem9uZVxuICpcbiAqIEBkZXNjcmlwdGlvblxuICogUmV0dXJucyBhIGRhdGUgaW5zdGFuY2Ugd2l0aCB0aGUgVVRDIHRpbWUgb2YgdGhlIHByb3ZpZGVkIGRhdGUgb2Ygd2hpY2ggdGhlIHZhbHVlc1xuICogcmVwcmVzZW50ZWQgdGhlIGxvY2FsIHRpbWUgaW4gdGhlIHRpbWUgem9uZSBzcGVjaWZpZWQuIEluIG90aGVyIHdvcmRzLCBpZiB0aGUgaW5wdXRcbiAqIGRhdGUgcmVwcmVzZW50ZWQgbG9jYWwgdGltZSBpbiB0aW1lIHpvbmUsIHRoZSB0aW1lc3RhbXAgb2YgdGhlIG91dHB1dCBkYXRlIHdpbGxcbiAqIGdpdmUgdGhlIGVxdWl2YWxlbnQgVVRDIG9mIHRoYXQgbG9jYWwgdGltZSByZWdhcmRsZXNzIG9mIHRoZSBjdXJyZW50IHN5c3RlbSB0aW1lIHpvbmUuXG4gKlxuICogQHBhcmFtIGRhdGUgdGhlIGRhdGUgd2l0aCB2YWx1ZXMgcmVwcmVzZW50aW5nIHRoZSBsb2NhbCB0aW1lXG4gKiBAcGFyYW0gdGltZVpvbmUgdGhlIHRpbWUgem9uZSBvZiB0aGlzIGxvY2FsIHRpbWUsIGNhbiBiZSBhbiBvZmZzZXQgb3IgSUFOQSB0aW1lIHpvbmVcbiAqIEBwYXJhbSBvcHRpb25zIHRoZSBvYmplY3Qgd2l0aCBvcHRpb25zLiBTZWUgW09wdGlvbnNde0BsaW5rIGh0dHBzOi8vZGF0ZS1mbnMub3JnL2RvY3MvT3B0aW9uc31cbiAqIEBwYXJhbSB7MHwxfDJ9IFtvcHRpb25zLmFkZGl0aW9uYWxEaWdpdHM9Ml0gLSBwYXNzZWQgdG8gYHRvRGF0ZWAuIFNlZSBbdG9EYXRlXXtAbGluayBodHRwczovL2RhdGUtZm5zLm9yZy9kb2NzL3RvRGF0ZX1cbiAqIEB0aHJvd3Mge1R5cGVFcnJvcn0gMiBhcmd1bWVudHMgcmVxdWlyZWRcbiAqIEB0aHJvd3Mge1JhbmdlRXJyb3J9IGBvcHRpb25zLmFkZGl0aW9uYWxEaWdpdHNgIG11c3QgYmUgMCwgMSBvciAyXG4gKlxuICogQGV4YW1wbGVcbiAqIC8vIEluIEp1bmUgMTBhbSBpbiBMb3MgQW5nZWxlcyBpcyA1cG0gVVRDXG4gKiBjb25zdCByZXN1bHQgPSBmcm9tWm9uZWRUaW1lKG5ldyBEYXRlKDIwMTQsIDUsIDI1LCAxMCwgMCwgMCksICdBbWVyaWNhL0xvc19BbmdlbGVzJylcbiAqIC8vPT4gMjAxNC0wNi0yNVQxNzowMDowMC4wMDBaXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBmcm9tWm9uZWRUaW1lKGRhdGUsIHRpbWVab25lLCBvcHRpb25zKSB7XG4gICAgaWYgKHR5cGVvZiBkYXRlID09PSAnc3RyaW5nJyAmJiAhZGF0ZS5tYXRjaCh0elBhdHRlcm4pKSB7XG4gICAgICAgIHJldHVybiB0b0RhdGUoZGF0ZSwgeyAuLi5vcHRpb25zLCB0aW1lWm9uZSB9KTtcbiAgICB9XG4gICAgZGF0ZSA9IHRvRGF0ZShkYXRlLCBvcHRpb25zKTtcbiAgICBjb25zdCB1dGMgPSBuZXdEYXRlVVRDKGRhdGUuZ2V0RnVsbFllYXIoKSwgZGF0ZS5nZXRNb250aCgpLCBkYXRlLmdldERhdGUoKSwgZGF0ZS5nZXRIb3VycygpLCBkYXRlLmdldE1pbnV0ZXMoKSwgZGF0ZS5nZXRTZWNvbmRzKCksIGRhdGUuZ2V0TWlsbGlzZWNvbmRzKCkpLmdldFRpbWUoKTtcbiAgICBjb25zdCBvZmZzZXRNaWxsaXNlY29uZHMgPSB0elBhcnNlVGltZXpvbmUodGltZVpvbmUsIG5ldyBEYXRlKHV0YykpO1xuICAgIHJldHVybiBuZXcgRGF0ZSh1dGMgKyBvZmZzZXRNaWxsaXNlY29uZHMpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/date-fns-tz/dist/esm/fromZonedTime/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/date-fns-tz/dist/esm/getTimezoneOffset/index.js":
/*!**********************************************************************!*\
  !*** ./node_modules/date-fns-tz/dist/esm/getTimezoneOffset/index.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTimezoneOffset: () => (/* binding */ getTimezoneOffset)\n/* harmony export */ });\n/* harmony import */ var _lib_tzParseTimezone_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_lib/tzParseTimezone/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/tzParseTimezone/index.js\");\n\n/**\n * @name getTimezoneOffset\n * @category Time Zone Helpers\n * @summary Gets the offset in milliseconds between the time zone and Universal Coordinated Time (UTC)\n *\n * @description\n * Returns the time zone offset from UTC time in milliseconds for IANA time zones as well\n * as other time zone offset string formats.\n *\n * For time zones where daylight savings time is applicable a `Date` should be passed on\n * the second parameter to ensure the offset correctly accounts for DST at that time of\n * year. When omitted, the current date is used.\n *\n * @param timeZone the time zone of this local time, can be an offset or IANA time zone\n * @param date the date with values representing the local time\n *\n * @example\n * const result = getTimezoneOffset('-07:00')\n *   //=> -******** (-7 * 60 * 60 * 1000)\n * const result = getTimezoneOffset('Africa/Johannesburg')\n *   //=> 7200000 (2 * 60 * 60 * 1000)\n * const result = getTimezoneOffset('America/New_York', new Date(2016, 0, 1))\n *   //=> -******** (-5 * 60 * 60 * 1000)\n * const result = getTimezoneOffset('America/New_York', new Date(2016, 6, 1))\n *   //=> -******** (-4 * 60 * 60 * 1000)\n */\nfunction getTimezoneOffset(timeZone, date) {\n    return -(0,_lib_tzParseTimezone_index_js__WEBPACK_IMPORTED_MODULE_0__.tzParseTimezone)(timeZone, date);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/date-fns-tz/dist/esm/getTimezoneOffset/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/date-fns-tz/dist/esm/index.js":
/*!****************************************************!*\
  !*** ./node_modules/date-fns-tz/dist/esm/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   format: () => (/* reexport safe */ _format_index_js__WEBPACK_IMPORTED_MODULE_0__.format),\n/* harmony export */   formatInTimeZone: () => (/* reexport safe */ _formatInTimeZone_index_js__WEBPACK_IMPORTED_MODULE_1__.formatInTimeZone),\n/* harmony export */   fromZonedTime: () => (/* reexport safe */ _fromZonedTime_index_js__WEBPACK_IMPORTED_MODULE_2__.fromZonedTime),\n/* harmony export */   getTimezoneOffset: () => (/* reexport safe */ _getTimezoneOffset_index_js__WEBPACK_IMPORTED_MODULE_4__.getTimezoneOffset),\n/* harmony export */   toDate: () => (/* reexport safe */ _toDate_index_js__WEBPACK_IMPORTED_MODULE_5__.toDate),\n/* harmony export */   toZonedTime: () => (/* reexport safe */ _toZonedTime_index_js__WEBPACK_IMPORTED_MODULE_3__.toZonedTime)\n/* harmony export */ });\n/* harmony import */ var _format_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./format/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/format/index.js\");\n/* harmony import */ var _formatInTimeZone_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./formatInTimeZone/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/formatInTimeZone/index.js\");\n/* harmony import */ var _fromZonedTime_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./fromZonedTime/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/fromZonedTime/index.js\");\n/* harmony import */ var _toZonedTime_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./toZonedTime/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/toZonedTime/index.js\");\n/* harmony import */ var _getTimezoneOffset_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./getTimezoneOffset/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/getTimezoneOffset/index.js\");\n/* harmony import */ var _toDate_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./toDate/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/toDate/index.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZGF0ZS1mbnMtdHovZGlzdC9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQTJDO0FBQ29CO0FBQ047QUFDSjtBQUNZO0FBQ3RCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJveGFzXFxPbmVEcml2ZVxcRGVza3RvcFxcUFJPSkVDVFNcXHR1Y3NvbmxvdmVzbXVzaWMuY29tXFxQcm90b3R5cGUxM1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZGF0ZS1mbnMtdHpcXGRpc3RcXGVzbVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgZm9ybWF0IH0gZnJvbSAnLi9mb3JtYXQvaW5kZXguanMnO1xuZXhwb3J0IHsgZm9ybWF0SW5UaW1lWm9uZSB9IGZyb20gJy4vZm9ybWF0SW5UaW1lWm9uZS9pbmRleC5qcyc7XG5leHBvcnQgeyBmcm9tWm9uZWRUaW1lIH0gZnJvbSAnLi9mcm9tWm9uZWRUaW1lL2luZGV4LmpzJztcbmV4cG9ydCB7IHRvWm9uZWRUaW1lIH0gZnJvbSAnLi90b1pvbmVkVGltZS9pbmRleC5qcyc7XG5leHBvcnQgeyBnZXRUaW1lem9uZU9mZnNldCB9IGZyb20gJy4vZ2V0VGltZXpvbmVPZmZzZXQvaW5kZXguanMnO1xuZXhwb3J0IHsgdG9EYXRlIH0gZnJvbSAnLi90b0RhdGUvaW5kZXguanMnO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/date-fns-tz/dist/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/date-fns-tz/dist/esm/toDate/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/date-fns-tz/dist/esm/toDate/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toDate: () => (/* binding */ toDate)\n/* harmony export */ });\n/* harmony import */ var _lib_getTimezoneOffsetInMilliseconds_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_lib/getTimezoneOffsetInMilliseconds/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/getTimezoneOffsetInMilliseconds/index.js\");\n/* harmony import */ var _lib_tzParseTimezone_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../_lib/tzParseTimezone/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/tzParseTimezone/index.js\");\n/* harmony import */ var _lib_tzPattern_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_lib/tzPattern/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/tzPattern/index.js\");\n\n\n\nconst MILLISECONDS_IN_HOUR = 3600000;\nconst MILLISECONDS_IN_MINUTE = 60000;\nconst DEFAULT_ADDITIONAL_DIGITS = 2;\nconst patterns = {\n    dateTimePattern: /^([0-9W+-]+)(T| )(.*)/,\n    datePattern: /^([0-9W+-]+)(.*)/,\n    plainTime: /:/,\n    // year tokens\n    YY: /^(\\d{2})$/,\n    YYY: [\n        /^([+-]\\d{2})$/, // 0 additional digits\n        /^([+-]\\d{3})$/, // 1 additional digit\n        /^([+-]\\d{4})$/, // 2 additional digits\n    ],\n    YYYY: /^(\\d{4})/,\n    YYYYY: [\n        /^([+-]\\d{4})/, // 0 additional digits\n        /^([+-]\\d{5})/, // 1 additional digit\n        /^([+-]\\d{6})/, // 2 additional digits\n    ],\n    // date tokens\n    MM: /^-(\\d{2})$/,\n    DDD: /^-?(\\d{3})$/,\n    MMDD: /^-?(\\d{2})-?(\\d{2})$/,\n    Www: /^-?W(\\d{2})$/,\n    WwwD: /^-?W(\\d{2})-?(\\d{1})$/,\n    HH: /^(\\d{2}([.,]\\d*)?)$/,\n    HHMM: /^(\\d{2}):?(\\d{2}([.,]\\d*)?)$/,\n    HHMMSS: /^(\\d{2}):?(\\d{2}):?(\\d{2}([.,]\\d*)?)$/,\n    // time zone tokens (to identify the presence of a tz)\n    timeZone: _lib_tzPattern_index_js__WEBPACK_IMPORTED_MODULE_2__.tzPattern,\n};\n/**\n * @name toDate\n * @category Common Helpers\n * @summary Convert the given argument to an instance of Date.\n *\n * @description\n * Convert the given argument to an instance of Date.\n *\n * If the argument is an instance of Date, the function returns its clone.\n *\n * If the argument is a number, it is treated as a timestamp.\n *\n * If an argument is a string, the function tries to parse it.\n * Function accepts complete ISO 8601 formats as well as partial implementations.\n * ISO 8601: http://en.wikipedia.org/wiki/ISO_8601\n * If the function cannot parse the string or the values are invalid, it returns Invalid Date.\n *\n * If the argument is none of the above, the function returns Invalid Date.\n *\n * **Note**: *all* Date arguments passed to any *date-fns* function is processed by `toDate`.\n * All *date-fns* functions will throw `RangeError` if `options.additionalDigits` is not 0, 1, 2 or undefined.\n *\n * @param argument the value to convert\n * @param options the object with options. See [Options]{@link https://date-fns.org/docs/Options}\n * @param {0|1|2} [options.additionalDigits=2] - the additional number of digits in the extended year format\n * @param {string} [options.timeZone=''] - used to specify the IANA time zone offset of a date String.\n *\n * @returns the parsed date in the local time zone\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.additionalDigits` must be 0, 1 or 2\n *\n * @example\n * // Convert string '2014-02-11T11:30:30' to date:\n * const result = toDate('2014-02-11T11:30:30')\n * //=> Tue Feb 11 2014 11:30:30\n *\n * @example\n * // Convert string '+02014101' to date,\n * // if the additional number of digits in the extended year format is 1:\n * const result = toDate('+02014101', {additionalDigits: 1})\n * //=> Fri Apr 11 2014 00:00:00\n */\nfunction toDate(argument, options = {}) {\n    if (arguments.length < 1) {\n        throw new TypeError('1 argument required, but only ' + arguments.length + ' present');\n    }\n    if (argument === null) {\n        return new Date(NaN);\n    }\n    const additionalDigits = options.additionalDigits == null ? DEFAULT_ADDITIONAL_DIGITS : Number(options.additionalDigits);\n    if (additionalDigits !== 2 && additionalDigits !== 1 && additionalDigits !== 0) {\n        throw new RangeError('additionalDigits must be 0, 1 or 2');\n    }\n    // Clone the date\n    if (argument instanceof Date ||\n        (typeof argument === 'object' && Object.prototype.toString.call(argument) === '[object Date]')) {\n        // Prevent the date to lose the milliseconds when passed to new Date() in IE10\n        return new Date(argument.getTime());\n    }\n    else if (typeof argument === 'number' ||\n        Object.prototype.toString.call(argument) === '[object Number]') {\n        return new Date(argument);\n    }\n    else if (!(Object.prototype.toString.call(argument) === '[object String]')) {\n        return new Date(NaN);\n    }\n    const dateStrings = splitDateString(argument);\n    const { year, restDateString } = parseYear(dateStrings.date, additionalDigits);\n    const date = parseDate(restDateString, year);\n    if (date === null || isNaN(date.getTime())) {\n        return new Date(NaN);\n    }\n    if (date) {\n        const timestamp = date.getTime();\n        let time = 0;\n        let offset;\n        if (dateStrings.time) {\n            time = parseTime(dateStrings.time);\n            if (time === null || isNaN(time)) {\n                return new Date(NaN);\n            }\n        }\n        if (dateStrings.timeZone || options.timeZone) {\n            offset = (0,_lib_tzParseTimezone_index_js__WEBPACK_IMPORTED_MODULE_1__.tzParseTimezone)(dateStrings.timeZone || options.timeZone, new Date(timestamp + time));\n            if (isNaN(offset)) {\n                return new Date(NaN);\n            }\n        }\n        else {\n            // get offset accurate to hour in time zones that change offset\n            offset = (0,_lib_getTimezoneOffsetInMilliseconds_index_js__WEBPACK_IMPORTED_MODULE_0__.getTimezoneOffsetInMilliseconds)(new Date(timestamp + time));\n            offset = (0,_lib_getTimezoneOffsetInMilliseconds_index_js__WEBPACK_IMPORTED_MODULE_0__.getTimezoneOffsetInMilliseconds)(new Date(timestamp + time + offset));\n        }\n        return new Date(timestamp + time + offset);\n    }\n    else {\n        return new Date(NaN);\n    }\n}\nfunction splitDateString(dateString) {\n    const dateStrings = {};\n    let parts = patterns.dateTimePattern.exec(dateString);\n    let timeString;\n    if (!parts) {\n        parts = patterns.datePattern.exec(dateString);\n        if (parts) {\n            dateStrings.date = parts[1];\n            timeString = parts[2];\n        }\n        else {\n            dateStrings.date = null;\n            timeString = dateString;\n        }\n    }\n    else {\n        dateStrings.date = parts[1];\n        timeString = parts[3];\n    }\n    if (timeString) {\n        const token = patterns.timeZone.exec(timeString);\n        if (token) {\n            dateStrings.time = timeString.replace(token[1], '');\n            dateStrings.timeZone = token[1].trim();\n        }\n        else {\n            dateStrings.time = timeString;\n        }\n    }\n    return dateStrings;\n}\nfunction parseYear(dateString, additionalDigits) {\n    if (dateString) {\n        const patternYYY = patterns.YYY[additionalDigits];\n        const patternYYYYY = patterns.YYYYY[additionalDigits];\n        // YYYY or ±YYYYY\n        let token = patterns.YYYY.exec(dateString) || patternYYYYY.exec(dateString);\n        if (token) {\n            const yearString = token[1];\n            return {\n                year: parseInt(yearString, 10),\n                restDateString: dateString.slice(yearString.length),\n            };\n        }\n        // YY or ±YYY\n        token = patterns.YY.exec(dateString) || patternYYY.exec(dateString);\n        if (token) {\n            const centuryString = token[1];\n            return {\n                year: parseInt(centuryString, 10) * 100,\n                restDateString: dateString.slice(centuryString.length),\n            };\n        }\n    }\n    // Invalid ISO-formatted year\n    return {\n        year: null,\n    };\n}\nfunction parseDate(dateString, year) {\n    // Invalid ISO-formatted year\n    if (year === null) {\n        return null;\n    }\n    let date;\n    let month;\n    let week;\n    // YYYY\n    if (!dateString || !dateString.length) {\n        date = new Date(0);\n        date.setUTCFullYear(year);\n        return date;\n    }\n    // YYYY-MM\n    let token = patterns.MM.exec(dateString);\n    if (token) {\n        date = new Date(0);\n        month = parseInt(token[1], 10) - 1;\n        if (!validateDate(year, month)) {\n            return new Date(NaN);\n        }\n        date.setUTCFullYear(year, month);\n        return date;\n    }\n    // YYYY-DDD or YYYYDDD\n    token = patterns.DDD.exec(dateString);\n    if (token) {\n        date = new Date(0);\n        const dayOfYear = parseInt(token[1], 10);\n        if (!validateDayOfYearDate(year, dayOfYear)) {\n            return new Date(NaN);\n        }\n        date.setUTCFullYear(year, 0, dayOfYear);\n        return date;\n    }\n    // yyyy-MM-dd or YYYYMMDD\n    token = patterns.MMDD.exec(dateString);\n    if (token) {\n        date = new Date(0);\n        month = parseInt(token[1], 10) - 1;\n        const day = parseInt(token[2], 10);\n        if (!validateDate(year, month, day)) {\n            return new Date(NaN);\n        }\n        date.setUTCFullYear(year, month, day);\n        return date;\n    }\n    // YYYY-Www or YYYYWww\n    token = patterns.Www.exec(dateString);\n    if (token) {\n        week = parseInt(token[1], 10) - 1;\n        if (!validateWeekDate(week)) {\n            return new Date(NaN);\n        }\n        return dayOfISOWeekYear(year, week);\n    }\n    // YYYY-Www-D or YYYYWwwD\n    token = patterns.WwwD.exec(dateString);\n    if (token) {\n        week = parseInt(token[1], 10) - 1;\n        const dayOfWeek = parseInt(token[2], 10) - 1;\n        if (!validateWeekDate(week, dayOfWeek)) {\n            return new Date(NaN);\n        }\n        return dayOfISOWeekYear(year, week, dayOfWeek);\n    }\n    // Invalid ISO-formatted date\n    return null;\n}\nfunction parseTime(timeString) {\n    let hours;\n    let minutes;\n    // hh\n    let token = patterns.HH.exec(timeString);\n    if (token) {\n        hours = parseFloat(token[1].replace(',', '.'));\n        if (!validateTime(hours)) {\n            return NaN;\n        }\n        return (hours % 24) * MILLISECONDS_IN_HOUR;\n    }\n    // hh:mm or hhmm\n    token = patterns.HHMM.exec(timeString);\n    if (token) {\n        hours = parseInt(token[1], 10);\n        minutes = parseFloat(token[2].replace(',', '.'));\n        if (!validateTime(hours, minutes)) {\n            return NaN;\n        }\n        return (hours % 24) * MILLISECONDS_IN_HOUR + minutes * MILLISECONDS_IN_MINUTE;\n    }\n    // hh:mm:ss or hhmmss\n    token = patterns.HHMMSS.exec(timeString);\n    if (token) {\n        hours = parseInt(token[1], 10);\n        minutes = parseInt(token[2], 10);\n        const seconds = parseFloat(token[3].replace(',', '.'));\n        if (!validateTime(hours, minutes, seconds)) {\n            return NaN;\n        }\n        return (hours % 24) * MILLISECONDS_IN_HOUR + minutes * MILLISECONDS_IN_MINUTE + seconds * 1000;\n    }\n    // Invalid ISO-formatted time\n    return null;\n}\nfunction dayOfISOWeekYear(isoWeekYear, week, day) {\n    week = week || 0;\n    day = day || 0;\n    const date = new Date(0);\n    date.setUTCFullYear(isoWeekYear, 0, 4);\n    const fourthOfJanuaryDay = date.getUTCDay() || 7;\n    const diff = week * 7 + day + 1 - fourthOfJanuaryDay;\n    date.setUTCDate(date.getUTCDate() + diff);\n    return date;\n}\n// Validation functions\nconst DAYS_IN_MONTH = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\nconst DAYS_IN_MONTH_LEAP_YEAR = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\nfunction isLeapYearIndex(year) {\n    return year % 400 === 0 || (year % 4 === 0 && year % 100 !== 0);\n}\nfunction validateDate(year, month, date) {\n    if (month < 0 || month > 11) {\n        return false;\n    }\n    if (date != null) {\n        if (date < 1) {\n            return false;\n        }\n        const isLeapYear = isLeapYearIndex(year);\n        if (isLeapYear && date > DAYS_IN_MONTH_LEAP_YEAR[month]) {\n            return false;\n        }\n        if (!isLeapYear && date > DAYS_IN_MONTH[month]) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction validateDayOfYearDate(year, dayOfYear) {\n    if (dayOfYear < 1) {\n        return false;\n    }\n    const isLeapYear = isLeapYearIndex(year);\n    if (isLeapYear && dayOfYear > 366) {\n        return false;\n    }\n    if (!isLeapYear && dayOfYear > 365) {\n        return false;\n    }\n    return true;\n}\nfunction validateWeekDate(week, day) {\n    if (week < 0 || week > 52) {\n        return false;\n    }\n    if (day != null && (day < 0 || day > 6)) {\n        return false;\n    }\n    return true;\n}\nfunction validateTime(hours, minutes, seconds) {\n    if (hours < 0 || hours >= 25) {\n        return false;\n    }\n    if (minutes != null && (minutes < 0 || minutes >= 60)) {\n        return false;\n    }\n    if (seconds != null && (seconds < 0 || seconds >= 60)) {\n        return false;\n    }\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/date-fns-tz/dist/esm/toDate/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/date-fns-tz/dist/esm/toZonedTime/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns-tz/dist/esm/toZonedTime/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toZonedTime: () => (/* binding */ toZonedTime)\n/* harmony export */ });\n/* harmony import */ var _lib_tzParseTimezone_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_lib/tzParseTimezone/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/_lib/tzParseTimezone/index.js\");\n/* harmony import */ var _toDate_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../toDate/index.js */ \"(ssr)/./node_modules/date-fns-tz/dist/esm/toDate/index.js\");\n\n\n/**\n * @name toZonedTime\n * @category Time Zone Helpers\n * @summary Get a date/time representing local time in a given time zone from the UTC date\n *\n * @description\n * Returns a date instance with values representing the local time in the time zone\n * specified of the UTC time from the date provided. In other words, when the new date\n * is formatted it will show the equivalent hours in the target time zone regardless\n * of the current system time zone.\n *\n * @param date the date with the relevant UTC time\n * @param timeZone the time zone to get local time for, can be an offset or IANA time zone\n * @param options the object with options. See [Options]{@link https://date-fns.org/docs/Options}\n * @param {0|1|2} [options.additionalDigits=2] - passed to `toDate`. See [toDate]{@link https://date-fns.org/docs/toDate}\n *\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.additionalDigits` must be 0, 1 or 2\n *\n * @example\n * // In June 10am UTC is 6am in New York (-04:00)\n * const result = toZonedTime('2014-06-25T10:00:00.000Z', 'America/New_York')\n * //=> Jun 25 2014 06:00:00\n */\nfunction toZonedTime(date, timeZone, options) {\n    date = (0,_toDate_index_js__WEBPACK_IMPORTED_MODULE_1__.toDate)(date, options);\n    const offsetMilliseconds = (0,_lib_tzParseTimezone_index_js__WEBPACK_IMPORTED_MODULE_0__.tzParseTimezone)(timeZone, date, true);\n    const d = new Date(date.getTime() - offsetMilliseconds);\n    const resultDate = new Date(0);\n    resultDate.setFullYear(d.getUTCFullYear(), d.getUTCMonth(), d.getUTCDate());\n    resultDate.setHours(d.getUTCHours(), d.getUTCMinutes(), d.getUTCSeconds(), d.getUTCMilliseconds());\n    return resultDate;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZGF0ZS1mbnMtdHovZGlzdC9lc20vdG9ab25lZFRpbWUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW1FO0FBQ3ZCO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseURBQXlEO0FBQ3pELFdBQVcsT0FBTyxnRUFBZ0U7QUFDbEY7QUFDQSxZQUFZLFdBQVc7QUFDdkIsWUFBWSxZQUFZO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsV0FBVyx3REFBTTtBQUNqQiwrQkFBK0IsOEVBQWU7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJveGFzXFxPbmVEcml2ZVxcRGVza3RvcFxcUFJPSkVDVFNcXHR1Y3NvbmxvdmVzbXVzaWMuY29tXFxQcm90b3R5cGUxM1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZGF0ZS1mbnMtdHpcXGRpc3RcXGVzbVxcdG9ab25lZFRpbWVcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR6UGFyc2VUaW1lem9uZSB9IGZyb20gJy4uL19saWIvdHpQYXJzZVRpbWV6b25lL2luZGV4LmpzJztcbmltcG9ydCB7IHRvRGF0ZSB9IGZyb20gJy4uL3RvRGF0ZS9pbmRleC5qcyc7XG4vKipcbiAqIEBuYW1lIHRvWm9uZWRUaW1lXG4gKiBAY2F0ZWdvcnkgVGltZSBab25lIEhlbHBlcnNcbiAqIEBzdW1tYXJ5IEdldCBhIGRhdGUvdGltZSByZXByZXNlbnRpbmcgbG9jYWwgdGltZSBpbiBhIGdpdmVuIHRpbWUgem9uZSBmcm9tIHRoZSBVVEMgZGF0ZVxuICpcbiAqIEBkZXNjcmlwdGlvblxuICogUmV0dXJucyBhIGRhdGUgaW5zdGFuY2Ugd2l0aCB2YWx1ZXMgcmVwcmVzZW50aW5nIHRoZSBsb2NhbCB0aW1lIGluIHRoZSB0aW1lIHpvbmVcbiAqIHNwZWNpZmllZCBvZiB0aGUgVVRDIHRpbWUgZnJvbSB0aGUgZGF0ZSBwcm92aWRlZC4gSW4gb3RoZXIgd29yZHMsIHdoZW4gdGhlIG5ldyBkYXRlXG4gKiBpcyBmb3JtYXR0ZWQgaXQgd2lsbCBzaG93IHRoZSBlcXVpdmFsZW50IGhvdXJzIGluIHRoZSB0YXJnZXQgdGltZSB6b25lIHJlZ2FyZGxlc3NcbiAqIG9mIHRoZSBjdXJyZW50IHN5c3RlbSB0aW1lIHpvbmUuXG4gKlxuICogQHBhcmFtIGRhdGUgdGhlIGRhdGUgd2l0aCB0aGUgcmVsZXZhbnQgVVRDIHRpbWVcbiAqIEBwYXJhbSB0aW1lWm9uZSB0aGUgdGltZSB6b25lIHRvIGdldCBsb2NhbCB0aW1lIGZvciwgY2FuIGJlIGFuIG9mZnNldCBvciBJQU5BIHRpbWUgem9uZVxuICogQHBhcmFtIG9wdGlvbnMgdGhlIG9iamVjdCB3aXRoIG9wdGlvbnMuIFNlZSBbT3B0aW9uc117QGxpbmsgaHR0cHM6Ly9kYXRlLWZucy5vcmcvZG9jcy9PcHRpb25zfVxuICogQHBhcmFtIHswfDF8Mn0gW29wdGlvbnMuYWRkaXRpb25hbERpZ2l0cz0yXSAtIHBhc3NlZCB0byBgdG9EYXRlYC4gU2VlIFt0b0RhdGVde0BsaW5rIGh0dHBzOi8vZGF0ZS1mbnMub3JnL2RvY3MvdG9EYXRlfVxuICpcbiAqIEB0aHJvd3Mge1R5cGVFcnJvcn0gMiBhcmd1bWVudHMgcmVxdWlyZWRcbiAqIEB0aHJvd3Mge1JhbmdlRXJyb3J9IGBvcHRpb25zLmFkZGl0aW9uYWxEaWdpdHNgIG11c3QgYmUgMCwgMSBvciAyXG4gKlxuICogQGV4YW1wbGVcbiAqIC8vIEluIEp1bmUgMTBhbSBVVEMgaXMgNmFtIGluIE5ldyBZb3JrICgtMDQ6MDApXG4gKiBjb25zdCByZXN1bHQgPSB0b1pvbmVkVGltZSgnMjAxNC0wNi0yNVQxMDowMDowMC4wMDBaJywgJ0FtZXJpY2EvTmV3X1lvcmsnKVxuICogLy89PiBKdW4gMjUgMjAxNCAwNjowMDowMFxuICovXG5leHBvcnQgZnVuY3Rpb24gdG9ab25lZFRpbWUoZGF0ZSwgdGltZVpvbmUsIG9wdGlvbnMpIHtcbiAgICBkYXRlID0gdG9EYXRlKGRhdGUsIG9wdGlvbnMpO1xuICAgIGNvbnN0IG9mZnNldE1pbGxpc2Vjb25kcyA9IHR6UGFyc2VUaW1lem9uZSh0aW1lWm9uZSwgZGF0ZSwgdHJ1ZSk7XG4gICAgY29uc3QgZCA9IG5ldyBEYXRlKGRhdGUuZ2V0VGltZSgpIC0gb2Zmc2V0TWlsbGlzZWNvbmRzKTtcbiAgICBjb25zdCByZXN1bHREYXRlID0gbmV3IERhdGUoMCk7XG4gICAgcmVzdWx0RGF0ZS5zZXRGdWxsWWVhcihkLmdldFVUQ0Z1bGxZZWFyKCksIGQuZ2V0VVRDTW9udGgoKSwgZC5nZXRVVENEYXRlKCkpO1xuICAgIHJlc3VsdERhdGUuc2V0SG91cnMoZC5nZXRVVENIb3VycygpLCBkLmdldFVUQ01pbnV0ZXMoKSwgZC5nZXRVVENTZWNvbmRzKCksIGQuZ2V0VVRDTWlsbGlzZWNvbmRzKCkpO1xuICAgIHJldHVybiByZXN1bHREYXRlO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/date-fns-tz/dist/esm/toZonedTime/index.js\n");

/***/ })

};
;