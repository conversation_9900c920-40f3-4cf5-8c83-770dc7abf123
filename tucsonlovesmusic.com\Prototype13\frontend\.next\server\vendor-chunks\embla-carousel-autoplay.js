"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/embla-carousel-autoplay";
exports.ids = ["vendor-chunks/embla-carousel-autoplay"];
exports.modules = {

/***/ "(ssr)/./node_modules/embla-carousel-autoplay/embla-carousel-autoplay.esm.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/embla-carousel-autoplay/embla-carousel-autoplay.esm.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Autoplay)\n/* harmony export */ });\nconst defaultOptions = {\n  active: true,\n  breakpoints: {},\n  delay: 4000,\n  jump: false,\n  playOnInit: true,\n  stopOnInteraction: true,\n  stopOnMouseEnter: false,\n  stopOnLastSnap: false,\n  rootNode: null\n};\n\nfunction Autoplay(userOptions = {}) {\n  let options;\n  let emblaApi;\n  let interaction;\n  let timer = 0;\n  let jump = false;\n  function init(emblaApiInstance, optionsHandler) {\n    emblaApi = emblaApiInstance;\n    const {\n      mergeOptions,\n      optionsAtMedia\n    } = optionsHandler;\n    const optionsBase = mergeOptions(defaultOptions, Autoplay.globalOptions);\n    const allOptions = mergeOptions(optionsBase, userOptions);\n    options = optionsAtMedia(allOptions);\n    jump = options.jump;\n    interaction = options.stopOnInteraction ? destroy : stop;\n    const {\n      eventStore,\n      ownerDocument,\n      ownerWindow\n    } = emblaApi.internalEngine();\n    const emblaRoot = emblaApi.rootNode();\n    const root = options.rootNode && options.rootNode(emblaRoot) || emblaRoot;\n    emblaApi.on('pointerDown', interaction);\n    if (!options.stopOnInteraction) emblaApi.on('pointerUp', reset);\n    if (options.stopOnMouseEnter) {\n      eventStore.add(root, 'mouseenter', interaction);\n      if (!options.stopOnInteraction) eventStore.add(root, 'mouseleave', reset);\n    }\n    eventStore.add(ownerDocument, 'visibilitychange', () => {\n      if (ownerDocument.visibilityState === 'hidden') return stop();\n      reset();\n    });\n    eventStore.add(ownerWindow, 'pagehide', event => {\n      if (event.persisted) stop();\n    });\n    if (options.playOnInit) play();\n  }\n  function destroy() {\n    emblaApi.off('pointerDown', interaction);\n    if (!options.stopOnInteraction) emblaApi.off('pointerUp', reset);\n    stop();\n    timer = 0;\n  }\n  function play(jumpOverride) {\n    stop();\n    if (typeof jumpOverride !== 'undefined') jump = jumpOverride;\n    timer = window.setTimeout(next, options.delay);\n  }\n  function stop() {\n    if (!timer) return;\n    window.clearTimeout(timer);\n  }\n  function reset() {\n    if (!timer) return;\n    stop();\n    play();\n  }\n  function next() {\n    const {\n      index\n    } = emblaApi.internalEngine();\n    const lastIndex = emblaApi.scrollSnapList().length - 1;\n    const kill = options.stopOnLastSnap && index.get() === lastIndex;\n    if (kill) return destroy();\n    if (emblaApi.canScrollNext()) {\n      emblaApi.scrollNext(jump);\n    } else {\n      emblaApi.scrollTo(0, jump);\n    }\n    play();\n  }\n  const self = {\n    name: 'autoplay',\n    options: userOptions,\n    init,\n    destroy,\n    play,\n    stop,\n    reset\n  };\n  return self;\n}\nAutoplay.globalOptions = undefined;\n\n\n//# sourceMappingURL=embla-carousel-autoplay.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/embla-carousel-autoplay/embla-carousel-autoplay.esm.js\n");

/***/ })

};
;