"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/embla-carousel";
exports.ids = ["vendor-chunks/embla-carousel"];
exports.modules = {

/***/ "(ssr)/./node_modules/embla-carousel/embla-carousel.esm.js":
/*!***********************************************************!*\
  !*** ./node_modules/embla-carousel/embla-carousel.esm.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EmblaCarousel)\n/* harmony export */ });\nfunction isNumber(subject) {\n  return typeof subject === 'number';\n}\nfunction isString(subject) {\n  return typeof subject === 'string';\n}\nfunction isBoolean(subject) {\n  return typeof subject === 'boolean';\n}\nfunction isObject(subject) {\n  return Object.prototype.toString.call(subject) === '[object Object]';\n}\nfunction mathAbs(n) {\n  return Math.abs(n);\n}\nfunction mathSign(n) {\n  return Math.sign(n);\n}\nfunction deltaAbs(valueB, valueA) {\n  return mathAbs(valueB - valueA);\n}\nfunction factorAbs(valueB, valueA) {\n  if (valueB === 0 || valueA === 0) return 0;\n  if (mathAbs(valueB) <= mathAbs(valueA)) return 0;\n  const diff = deltaAbs(mathAbs(valueB), mathAbs(valueA));\n  return mathAbs(diff / valueB);\n}\nfunction arrayKeys(array) {\n  return objectKeys(array).map(Number);\n}\nfunction arrayLast(array) {\n  return array[arrayLastIndex(array)];\n}\nfunction arrayLastIndex(array) {\n  return Math.max(0, array.length - 1);\n}\nfunction arrayFromNumber(n, startAt = 0) {\n  return Array.from(Array(n), (_, i) => startAt + i);\n}\nfunction objectKeys(object) {\n  return Object.keys(object);\n}\nfunction objectsMergeDeep(objectA, objectB) {\n  return [objectA, objectB].reduce((mergedObjects, currentObject) => {\n    objectKeys(currentObject).forEach(key => {\n      const valueA = mergedObjects[key];\n      const valueB = currentObject[key];\n      const areObjects = isObject(valueA) && isObject(valueB);\n      mergedObjects[key] = areObjects ? objectsMergeDeep(valueA, valueB) : valueB;\n    });\n    return mergedObjects;\n  }, {});\n}\nfunction isMouseEvent(evt, ownerWindow) {\n  return typeof ownerWindow.MouseEvent !== 'undefined' && evt instanceof ownerWindow.MouseEvent;\n}\n\nfunction Alignment(align, viewSize) {\n  const predefined = {\n    start,\n    center,\n    end\n  };\n  function start() {\n    return 0;\n  }\n  function center(n) {\n    return end(n) / 2;\n  }\n  function end(n) {\n    return viewSize - n;\n  }\n  function percent() {\n    return viewSize * Number(align);\n  }\n  function measure(n) {\n    if (isNumber(align)) return percent();\n    return predefined[align](n);\n  }\n  const self = {\n    measure\n  };\n  return self;\n}\n\nfunction Axis(axis, direction) {\n  const scroll = axis === 'y' ? 'y' : 'x';\n  const cross = axis === 'y' ? 'x' : 'y';\n  const startEdge = getStartEdge();\n  const endEdge = getEndEdge();\n  function measureSize(rect) {\n    const {\n      width,\n      height\n    } = rect;\n    return scroll === 'x' ? width : height;\n  }\n  function getStartEdge() {\n    if (scroll === 'y') return 'top';\n    return direction === 'rtl' ? 'right' : 'left';\n  }\n  function getEndEdge() {\n    if (scroll === 'y') return 'bottom';\n    return direction === 'rtl' ? 'left' : 'right';\n  }\n  const self = {\n    scroll,\n    cross,\n    startEdge,\n    endEdge,\n    measureSize\n  };\n  return self;\n}\n\nfunction Limit(min, max) {\n  const length = mathAbs(min - max);\n  function reachedMin(n) {\n    return n < min;\n  }\n  function reachedMax(n) {\n    return n > max;\n  }\n  function reachedAny(n) {\n    return reachedMin(n) || reachedMax(n);\n  }\n  function constrain(n) {\n    if (!reachedAny(n)) return n;\n    return reachedMin(n) ? min : max;\n  }\n  function removeOffset(n) {\n    if (!length) return n;\n    return n - length * Math.ceil((n - max) / length);\n  }\n  const self = {\n    length,\n    max,\n    min,\n    constrain,\n    reachedAny,\n    reachedMax,\n    reachedMin,\n    removeOffset\n  };\n  return self;\n}\n\nfunction Counter(max, start, loop) {\n  const {\n    constrain\n  } = Limit(0, max);\n  const loopEnd = max + 1;\n  let counter = withinLimit(start);\n  function withinLimit(n) {\n    return !loop ? constrain(n) : mathAbs((loopEnd + n) % loopEnd);\n  }\n  function get() {\n    return counter;\n  }\n  function set(n) {\n    counter = withinLimit(n);\n    return self;\n  }\n  function add(n) {\n    return clone().set(get() + n);\n  }\n  function clone() {\n    return Counter(max, get(), loop);\n  }\n  const self = {\n    get,\n    set,\n    add,\n    clone\n  };\n  return self;\n}\n\nfunction Direction(direction) {\n  const sign = direction === 'rtl' ? -1 : 1;\n  function apply(n) {\n    return n * sign;\n  }\n  const self = {\n    apply\n  };\n  return self;\n}\n\nfunction EventStore() {\n  let listeners = [];\n  function add(node, type, handler, options = {\n    passive: true\n  }) {\n    let removeListener;\n    if ('addEventListener' in node) {\n      node.addEventListener(type, handler, options);\n      removeListener = () => node.removeEventListener(type, handler, options);\n    } else {\n      const legacyMediaQueryList = node;\n      legacyMediaQueryList.addListener(handler);\n      removeListener = () => legacyMediaQueryList.removeListener(handler);\n    }\n    listeners.push(removeListener);\n    return self;\n  }\n  function clear() {\n    listeners = listeners.filter(remove => remove());\n  }\n  const self = {\n    add,\n    clear\n  };\n  return self;\n}\n\nfunction DragHandler(axis, direction, rootNode, ownerDocument, ownerWindow, target, dragTracker, location, animation, scrollTo, scrollBody, scrollTarget, index, eventHandler, percentOfView, dragFree, dragThreshold, skipSnaps, baseFriction, watchDrag) {\n  const {\n    cross: crossAxis\n  } = axis;\n  const focusNodes = ['INPUT', 'SELECT', 'TEXTAREA'];\n  const nonPassiveEvent = {\n    passive: false\n  };\n  const initEvents = EventStore();\n  const dragEvents = EventStore();\n  const goToNextThreshold = Limit(50, 225).constrain(percentOfView.measure(20));\n  const snapForceBoost = {\n    mouse: 300,\n    touch: 400\n  };\n  const freeForceBoost = {\n    mouse: 500,\n    touch: 600\n  };\n  const baseSpeed = dragFree ? 43 : 25;\n  let isMoving = false;\n  let startScroll = 0;\n  let startCross = 0;\n  let pointerIsDown = false;\n  let preventScroll = false;\n  let preventClick = false;\n  let isMouse = false;\n  function init(emblaApi) {\n    if (!watchDrag) return;\n    function downIfAllowed(evt) {\n      if (isBoolean(watchDrag) || watchDrag(emblaApi, evt)) down(evt);\n    }\n    const node = rootNode;\n    initEvents.add(node, 'dragstart', evt => evt.preventDefault(), nonPassiveEvent).add(node, 'touchmove', () => undefined, nonPassiveEvent).add(node, 'touchend', () => undefined).add(node, 'touchstart', downIfAllowed).add(node, 'mousedown', downIfAllowed).add(node, 'touchcancel', up).add(node, 'contextmenu', up).add(node, 'click', click, true);\n  }\n  function destroy() {\n    initEvents.clear();\n    dragEvents.clear();\n  }\n  function addDragEvents() {\n    const node = isMouse ? ownerDocument : rootNode;\n    dragEvents.add(node, 'touchmove', move, nonPassiveEvent).add(node, 'touchend', up).add(node, 'mousemove', move, nonPassiveEvent).add(node, 'mouseup', up);\n  }\n  function isFocusNode(node) {\n    const nodeName = node.nodeName || '';\n    return focusNodes.includes(nodeName);\n  }\n  function forceBoost() {\n    const boost = dragFree ? freeForceBoost : snapForceBoost;\n    const type = isMouse ? 'mouse' : 'touch';\n    return boost[type];\n  }\n  function allowedForce(force, targetChanged) {\n    const next = index.add(mathSign(force) * -1);\n    const baseForce = scrollTarget.byDistance(force, !dragFree).distance;\n    if (dragFree || mathAbs(force) < goToNextThreshold) return baseForce;\n    if (skipSnaps && targetChanged) return baseForce * 0.5;\n    return scrollTarget.byIndex(next.get(), 0).distance;\n  }\n  function down(evt) {\n    const isMouseEvt = isMouseEvent(evt, ownerWindow);\n    isMouse = isMouseEvt;\n    if (isMouseEvt && evt.button !== 0) return;\n    if (isFocusNode(evt.target)) return;\n    preventClick = dragFree && isMouseEvt && !evt.buttons && isMoving;\n    isMoving = deltaAbs(target.get(), location.get()) >= 2;\n    pointerIsDown = true;\n    dragTracker.pointerDown(evt);\n    scrollBody.useFriction(0).useDuration(0);\n    target.set(location);\n    addDragEvents();\n    startScroll = dragTracker.readPoint(evt);\n    startCross = dragTracker.readPoint(evt, crossAxis);\n    eventHandler.emit('pointerDown');\n  }\n  function move(evt) {\n    const lastScroll = dragTracker.readPoint(evt);\n    const lastCross = dragTracker.readPoint(evt, crossAxis);\n    const diffScroll = deltaAbs(lastScroll, startScroll);\n    const diffCross = deltaAbs(lastCross, startCross);\n    if (!preventScroll && !isMouse) {\n      if (!evt.cancelable) return up(evt);\n      preventScroll = diffScroll > diffCross;\n      if (!preventScroll) return up(evt);\n    }\n    const diff = dragTracker.pointerMove(evt);\n    if (diffScroll > dragThreshold) preventClick = true;\n    scrollBody.useFriction(0.3).useDuration(1);\n    animation.start();\n    target.add(direction.apply(diff));\n    evt.preventDefault();\n  }\n  function up(evt) {\n    const currentLocation = scrollTarget.byDistance(0, false);\n    const targetChanged = currentLocation.index !== index.get();\n    const rawForce = dragTracker.pointerUp(evt) * forceBoost();\n    const force = allowedForce(direction.apply(rawForce), targetChanged);\n    const forceFactor = factorAbs(rawForce, force);\n    const speed = baseSpeed - 10 * forceFactor;\n    const friction = baseFriction + forceFactor / 50;\n    preventScroll = false;\n    pointerIsDown = false;\n    dragEvents.clear();\n    scrollBody.useDuration(speed).useFriction(friction);\n    scrollTo.distance(force, !dragFree);\n    isMouse = false;\n    eventHandler.emit('pointerUp');\n  }\n  function click(evt) {\n    if (preventClick) {\n      evt.stopPropagation();\n      evt.preventDefault();\n    }\n  }\n  function pointerDown() {\n    return pointerIsDown;\n  }\n  const self = {\n    init,\n    pointerDown,\n    destroy\n  };\n  return self;\n}\n\nfunction DragTracker(axis, ownerWindow) {\n  const logInterval = 170;\n  let startEvent;\n  let lastEvent;\n  function readTime(evt) {\n    return evt.timeStamp;\n  }\n  function readPoint(evt, evtAxis) {\n    const property = evtAxis || axis.scroll;\n    const coord = `client${property === 'x' ? 'X' : 'Y'}`;\n    return (isMouseEvent(evt, ownerWindow) ? evt : evt.touches[0])[coord];\n  }\n  function pointerDown(evt) {\n    startEvent = evt;\n    lastEvent = evt;\n    return readPoint(evt);\n  }\n  function pointerMove(evt) {\n    const diff = readPoint(evt) - readPoint(lastEvent);\n    const expired = readTime(evt) - readTime(startEvent) > logInterval;\n    lastEvent = evt;\n    if (expired) startEvent = evt;\n    return diff;\n  }\n  function pointerUp(evt) {\n    if (!startEvent || !lastEvent) return 0;\n    const diffDrag = readPoint(lastEvent) - readPoint(startEvent);\n    const diffTime = readTime(evt) - readTime(startEvent);\n    const expired = readTime(evt) - readTime(lastEvent) > logInterval;\n    const force = diffDrag / diffTime;\n    const isFlick = diffTime && !expired && mathAbs(force) > 0.1;\n    return isFlick ? force : 0;\n  }\n  const self = {\n    pointerDown,\n    pointerMove,\n    pointerUp,\n    readPoint\n  };\n  return self;\n}\n\nfunction PercentOfView(viewSize) {\n  function measure(n) {\n    return viewSize * (n / 100);\n  }\n  const self = {\n    measure\n  };\n  return self;\n}\n\nfunction ResizeHandler(container, eventHandler, ownerWindow, slides, axis, watchResize) {\n  let resizeObserver;\n  let containerSize;\n  let slideSizes = [];\n  let destroyed = false;\n  function readSize(node) {\n    return axis.measureSize(node.getBoundingClientRect());\n  }\n  function init(emblaApi) {\n    if (!watchResize) return;\n    containerSize = readSize(container);\n    slideSizes = slides.map(readSize);\n    function defaultCallback(entries) {\n      for (const entry of entries) {\n        const isContainer = entry.target === container;\n        const slideIndex = slides.indexOf(entry.target);\n        const lastSize = isContainer ? containerSize : slideSizes[slideIndex];\n        const newSize = readSize(isContainer ? container : slides[slideIndex]);\n        const diffSize = mathAbs(newSize - lastSize);\n        if (diffSize >= 0.2) {\n          ownerWindow.requestAnimationFrame(() => {\n            emblaApi.reInit();\n            eventHandler.emit('resize');\n          });\n          break;\n        }\n      }\n    }\n    resizeObserver = new ResizeObserver(entries => {\n      if (destroyed) return;\n      if (isBoolean(watchResize) || watchResize(emblaApi, entries)) {\n        defaultCallback(entries);\n      }\n    });\n    const observeNodes = [container].concat(slides);\n    observeNodes.forEach(node => resizeObserver.observe(node));\n  }\n  function destroy() {\n    if (resizeObserver) resizeObserver.disconnect();\n    destroyed = true;\n  }\n  const self = {\n    init,\n    destroy\n  };\n  return self;\n}\n\nfunction ScrollBody(location, target, baseDuration, baseFriction) {\n  let hasSettled = true;\n  let bodyVelocity = 0;\n  let scrollDirection = 0;\n  let scrollDuration = baseDuration;\n  let scrollFriction = baseFriction;\n  let rawLocation = location.get();\n  let rawLocationPrevious = 0;\n  function seek() {\n    const diff = target.get() - location.get();\n    const isInstant = !scrollDuration;\n    let directionDiff = 0;\n    if (isInstant) {\n      bodyVelocity = 0;\n      location.set(target);\n      directionDiff = diff;\n    } else {\n      bodyVelocity += diff / scrollDuration;\n      bodyVelocity *= scrollFriction;\n      rawLocation += bodyVelocity;\n      location.add(bodyVelocity);\n      directionDiff = rawLocation - rawLocationPrevious;\n    }\n    scrollDirection = mathSign(directionDiff);\n    rawLocationPrevious = rawLocation;\n    hasSettled = mathAbs(diff) < 0.001;\n    return self;\n  }\n  function settled() {\n    return hasSettled;\n  }\n  function duration() {\n    return scrollDuration;\n  }\n  function direction() {\n    return scrollDirection;\n  }\n  function velocity() {\n    return bodyVelocity;\n  }\n  function useBaseDuration() {\n    return useDuration(baseDuration);\n  }\n  function useBaseFriction() {\n    return useFriction(baseFriction);\n  }\n  function useDuration(n) {\n    scrollDuration = n;\n    return self;\n  }\n  function useFriction(n) {\n    scrollFriction = n;\n    return self;\n  }\n  const self = {\n    direction,\n    duration,\n    velocity,\n    seek,\n    settled,\n    useBaseFriction,\n    useBaseDuration,\n    useFriction,\n    useDuration\n  };\n  return self;\n}\n\nfunction ScrollBounds(limit, location, target, scrollBody, percentOfView) {\n  const pullBackThreshold = percentOfView.measure(10);\n  const edgeOffsetTolerance = percentOfView.measure(50);\n  const frictionLimit = Limit(0.1, 0.99);\n  let disabled = false;\n  function shouldConstrain() {\n    if (disabled) return false;\n    if (!limit.reachedAny(target.get())) return false;\n    if (!limit.reachedAny(location.get())) return false;\n    return true;\n  }\n  function constrain(pointerDown) {\n    if (!shouldConstrain()) return;\n    const edge = limit.reachedMin(location.get()) ? 'min' : 'max';\n    const diffToEdge = mathAbs(limit[edge] - location.get());\n    const diffToTarget = target.get() - location.get();\n    const friction = frictionLimit.constrain(diffToEdge / edgeOffsetTolerance);\n    target.subtract(diffToTarget * friction);\n    if (!pointerDown && mathAbs(diffToTarget) < pullBackThreshold) {\n      target.set(limit.constrain(target.get()));\n      scrollBody.useDuration(25).useBaseFriction();\n    }\n  }\n  function toggleActive(active) {\n    disabled = !active;\n  }\n  const self = {\n    constrain,\n    toggleActive\n  };\n  return self;\n}\n\nfunction ScrollContain(viewSize, contentSize, snapsAligned, containScroll) {\n  const scrollBounds = Limit(-contentSize + viewSize, 0);\n  const snapsBounded = measureBounded();\n  const scrollContainLimit = findScrollContainLimit();\n  const snapsContained = measureContained();\n  function findScrollContainLimit() {\n    const startSnap = snapsBounded[0];\n    const endSnap = arrayLast(snapsBounded);\n    const min = snapsBounded.lastIndexOf(startSnap);\n    const max = snapsBounded.indexOf(endSnap) + 1;\n    return Limit(min, max);\n  }\n  function measureBounded() {\n    return snapsAligned.map(scrollBounds.constrain).map(scrollBound => parseFloat(scrollBound.toFixed(3)));\n  }\n  function measureContained() {\n    if (contentSize <= viewSize) return [scrollBounds.max];\n    if (containScroll === 'keepSnaps') return snapsBounded;\n    const {\n      min,\n      max\n    } = scrollContainLimit;\n    return snapsBounded.slice(min, max);\n  }\n  const self = {\n    snapsContained,\n    scrollContainLimit\n  };\n  return self;\n}\n\nfunction ScrollLimit(contentSize, scrollSnaps, loop) {\n  const max = scrollSnaps[0];\n  const min = loop ? max - contentSize : arrayLast(scrollSnaps);\n  const limit = Limit(min, max);\n  const self = {\n    limit\n  };\n  return self;\n}\n\nfunction ScrollLooper(contentSize, limit, offsetLocation, vectors) {\n  const jointSafety = 0.1;\n  const min = limit.min + jointSafety;\n  const max = limit.max + jointSafety;\n  const {\n    reachedMin,\n    reachedMax\n  } = Limit(min, max);\n  function shouldLoop(direction) {\n    if (direction === 1) return reachedMax(offsetLocation.get());\n    if (direction === -1) return reachedMin(offsetLocation.get());\n    return false;\n  }\n  function loop(direction) {\n    if (!shouldLoop(direction)) return;\n    const loopDistance = contentSize * (direction * -1);\n    vectors.forEach(v => v.add(loopDistance));\n  }\n  const self = {\n    loop\n  };\n  return self;\n}\n\nfunction ScrollProgress(limit) {\n  const {\n    max,\n    length\n  } = limit;\n  function get(n) {\n    const currentLocation = n - max;\n    return length ? currentLocation / -length : 0;\n  }\n  const self = {\n    get\n  };\n  return self;\n}\n\nfunction ScrollSnaps(axis, alignment, containerRect, slideRects, slidesToScroll) {\n  const {\n    startEdge,\n    endEdge\n  } = axis;\n  const {\n    groupSlides\n  } = slidesToScroll;\n  const alignments = measureSizes().map(alignment.measure);\n  const snaps = measureUnaligned();\n  const snapsAligned = measureAligned();\n  function measureSizes() {\n    return groupSlides(slideRects).map(rects => arrayLast(rects)[endEdge] - rects[0][startEdge]).map(mathAbs);\n  }\n  function measureUnaligned() {\n    return slideRects.map(rect => containerRect[startEdge] - rect[startEdge]).map(snap => -mathAbs(snap));\n  }\n  function measureAligned() {\n    return groupSlides(snaps).map(g => g[0]).map((snap, index) => snap + alignments[index]);\n  }\n  const self = {\n    snaps,\n    snapsAligned\n  };\n  return self;\n}\n\nfunction SlideRegistry(viewSize, contentSize, containSnaps, scrollContainLimit, slidesToScroll, slideIndexes) {\n  const {\n    groupSlides\n  } = slidesToScroll;\n  const {\n    min,\n    max\n  } = scrollContainLimit;\n  const slideRegistry = createSlideRegistry();\n  function createSlideRegistry() {\n    const groupedSlideIndexes = groupSlides(slideIndexes);\n    if (!containSnaps || contentSize <= viewSize) return groupedSlideIndexes;\n    return groupedSlideIndexes.slice(min, max).map((group, index, groups) => {\n      const indexIsFirst = !index;\n      const indexIsLast = !indexIsFirst && index === arrayLastIndex(groups);\n      if (indexIsFirst) {\n        const range = arrayLast(groups[0]) + 1;\n        return arrayFromNumber(range);\n      }\n      if (indexIsLast) {\n        const range = arrayLastIndex(slideIndexes) - arrayLast(groups)[0] + 1;\n        return arrayFromNumber(range, arrayLast(groups)[0]);\n      }\n      return group;\n    });\n  }\n  const self = {\n    slideRegistry\n  };\n  return self;\n}\n\nfunction ScrollTarget(loop, scrollSnaps, contentSize, limit, targetVector) {\n  const {\n    reachedAny,\n    removeOffset,\n    constrain\n  } = limit;\n  function minDistance(distances) {\n    return distances.concat().sort((a, b) => mathAbs(a) - mathAbs(b))[0];\n  }\n  function findTargetSnap(target) {\n    const distance = loop ? removeOffset(target) : constrain(target);\n    const ascDiffsToSnaps = scrollSnaps.map(scrollSnap => scrollSnap - distance).map(diffToSnap => shortcut(diffToSnap, 0)).map((diff, i) => ({\n      diff,\n      index: i\n    })).sort((d1, d2) => mathAbs(d1.diff) - mathAbs(d2.diff));\n    const {\n      index\n    } = ascDiffsToSnaps[0];\n    return {\n      index,\n      distance\n    };\n  }\n  function shortcut(target, direction) {\n    const targets = [target, target + contentSize, target - contentSize];\n    if (!loop) return targets[0];\n    if (!direction) return minDistance(targets);\n    const matchingTargets = targets.filter(t => mathSign(t) === direction);\n    if (matchingTargets.length) return minDistance(matchingTargets);\n    return arrayLast(targets) - contentSize;\n  }\n  function byIndex(index, direction) {\n    const diffToSnap = scrollSnaps[index] - targetVector.get();\n    const distance = shortcut(diffToSnap, direction);\n    return {\n      index,\n      distance\n    };\n  }\n  function byDistance(distance, snap) {\n    const target = targetVector.get() + distance;\n    const {\n      index,\n      distance: targetSnapDistance\n    } = findTargetSnap(target);\n    const reachedBound = !loop && reachedAny(target);\n    if (!snap || reachedBound) return {\n      index,\n      distance\n    };\n    const diffToSnap = scrollSnaps[index] - targetSnapDistance;\n    const snapDistance = distance + shortcut(diffToSnap, 0);\n    return {\n      index,\n      distance: snapDistance\n    };\n  }\n  const self = {\n    byDistance,\n    byIndex,\n    shortcut\n  };\n  return self;\n}\n\nfunction ScrollTo(animation, indexCurrent, indexPrevious, scrollTarget, scrollBody, targetVector, eventHandler) {\n  function scrollTo(target) {\n    const distanceDiff = target.distance;\n    const indexDiff = target.index !== indexCurrent.get();\n    targetVector.add(distanceDiff);\n    if (distanceDiff) {\n      if (scrollBody.duration()) {\n        animation.start();\n      } else {\n        animation.update();\n        animation.render(1);\n        animation.update();\n      }\n    }\n    if (indexDiff) {\n      indexPrevious.set(indexCurrent.get());\n      indexCurrent.set(target.index);\n      eventHandler.emit('select');\n    }\n  }\n  function distance(n, snap) {\n    const target = scrollTarget.byDistance(n, snap);\n    scrollTo(target);\n  }\n  function index(n, direction) {\n    const targetIndex = indexCurrent.clone().set(n);\n    const target = scrollTarget.byIndex(targetIndex.get(), direction);\n    scrollTo(target);\n  }\n  const self = {\n    distance,\n    index\n  };\n  return self;\n}\n\nfunction SlideFocus(root, slides, slideRegistry, scrollTo, scrollBody, eventStore) {\n  let lastTabPressTime = 0;\n  function init() {\n    eventStore.add(document, 'keydown', registerTabPress, false);\n    slides.forEach(addSlideFocusEvent);\n  }\n  function registerTabPress(event) {\n    if (event.code === 'Tab') lastTabPressTime = new Date().getTime();\n  }\n  function addSlideFocusEvent(slide) {\n    const focus = () => {\n      const nowTime = new Date().getTime();\n      const diffTime = nowTime - lastTabPressTime;\n      if (diffTime > 10) return;\n      root.scrollLeft = 0;\n      const index = slides.indexOf(slide);\n      const group = slideRegistry.findIndex(group => group.includes(index));\n      if (!isNumber(group)) return;\n      scrollBody.useDuration(0);\n      scrollTo.index(group, 0);\n    };\n    eventStore.add(slide, 'focus', focus, {\n      passive: true,\n      capture: true\n    });\n  }\n  const self = {\n    init\n  };\n  return self;\n}\n\nfunction Vector1D(initialValue) {\n  let value = initialValue;\n  function get() {\n    return value;\n  }\n  function set(n) {\n    value = normalizeInput(n);\n  }\n  function add(n) {\n    value += normalizeInput(n);\n  }\n  function subtract(n) {\n    value -= normalizeInput(n);\n  }\n  function normalizeInput(n) {\n    return isNumber(n) ? n : n.get();\n  }\n  const self = {\n    get,\n    set,\n    add,\n    subtract\n  };\n  return self;\n}\n\nfunction Translate(axis, direction, container) {\n  const translate = axis.scroll === 'x' ? x : y;\n  const containerStyle = container.style;\n  let disabled = false;\n  function x(n) {\n    return `translate3d(${n}px,0px,0px)`;\n  }\n  function y(n) {\n    return `translate3d(0px,${n}px,0px)`;\n  }\n  function to(target) {\n    if (disabled) return;\n    containerStyle.transform = translate(direction.apply(target));\n  }\n  function toggleActive(active) {\n    disabled = !active;\n  }\n  function clear() {\n    if (disabled) return;\n    containerStyle.transform = '';\n    if (!container.getAttribute('style')) container.removeAttribute('style');\n  }\n  const self = {\n    clear,\n    to,\n    toggleActive\n  };\n  return self;\n}\n\nfunction SlideLooper(axis, direction, viewSize, contentSize, slideSizes, slideSizesWithGaps, snaps, scrollSnaps, offsetLocation, slides) {\n  const roundingSafety = 0.5;\n  const ascItems = arrayKeys(slideSizesWithGaps);\n  const descItems = arrayKeys(slideSizesWithGaps).reverse();\n  const loopPoints = startPoints().concat(endPoints());\n  function removeSlideSizes(indexes, from) {\n    return indexes.reduce((a, i) => {\n      return a - slideSizesWithGaps[i];\n    }, from);\n  }\n  function slidesInGap(indexes, gap) {\n    return indexes.reduce((a, i) => {\n      const remainingGap = removeSlideSizes(a, gap);\n      return remainingGap > 0 ? a.concat([i]) : a;\n    }, []);\n  }\n  function findSlideBounds(offset) {\n    return snaps.map((snap, index) => ({\n      start: snap - slideSizes[index] + roundingSafety + offset,\n      end: snap + viewSize - roundingSafety + offset\n    }));\n  }\n  function findLoopPoints(indexes, offset, isEndEdge) {\n    const slideBounds = findSlideBounds(offset);\n    return indexes.map(index => {\n      const initial = isEndEdge ? 0 : -contentSize;\n      const altered = isEndEdge ? contentSize : 0;\n      const boundEdge = isEndEdge ? 'end' : 'start';\n      const loopPoint = slideBounds[index][boundEdge];\n      return {\n        index,\n        slideLocation: Vector1D(-1),\n        translate: Translate(axis, direction, slides[index]),\n        target: () => offsetLocation.get() > loopPoint ? initial : altered\n      };\n    });\n  }\n  function startPoints() {\n    const gap = scrollSnaps[0] - 1;\n    const indexes = slidesInGap(descItems, gap);\n    return findLoopPoints(indexes, contentSize, false);\n  }\n  function endPoints() {\n    const gap = viewSize - scrollSnaps[0] - 1;\n    const indexes = slidesInGap(ascItems, gap);\n    return findLoopPoints(indexes, -contentSize, true);\n  }\n  function canLoop() {\n    return loopPoints.every(({\n      index\n    }) => {\n      const otherIndexes = ascItems.filter(i => i !== index);\n      return removeSlideSizes(otherIndexes, viewSize) <= 0.1;\n    });\n  }\n  function loop() {\n    loopPoints.forEach(loopPoint => {\n      const {\n        target,\n        translate,\n        slideLocation\n      } = loopPoint;\n      const shiftLocation = target();\n      if (shiftLocation === slideLocation.get()) return;\n      translate.to(shiftLocation);\n      slideLocation.set(shiftLocation);\n    });\n  }\n  function clear() {\n    loopPoints.forEach(loopPoint => loopPoint.translate.clear());\n  }\n  const self = {\n    canLoop,\n    clear,\n    loop,\n    loopPoints\n  };\n  return self;\n}\n\nfunction SlidesHandler(container, eventHandler, watchSlides) {\n  let mutationObserver;\n  let destroyed = false;\n  function init(emblaApi) {\n    if (!watchSlides) return;\n    function defaultCallback(mutations) {\n      for (const mutation of mutations) {\n        if (mutation.type === 'childList') {\n          emblaApi.reInit();\n          eventHandler.emit('slidesChanged');\n          break;\n        }\n      }\n    }\n    mutationObserver = new MutationObserver(mutations => {\n      if (destroyed) return;\n      if (isBoolean(watchSlides) || watchSlides(emblaApi, mutations)) {\n        defaultCallback(mutations);\n      }\n    });\n    mutationObserver.observe(container, {\n      childList: true\n    });\n  }\n  function destroy() {\n    if (mutationObserver) mutationObserver.disconnect();\n    destroyed = true;\n  }\n  const self = {\n    init,\n    destroy\n  };\n  return self;\n}\n\nfunction SlidesInView(container, slides, eventHandler, threshold) {\n  const intersectionEntryMap = {};\n  let inViewCache = null;\n  let notInViewCache = null;\n  let intersectionObserver;\n  let destroyed = false;\n  function init() {\n    intersectionObserver = new IntersectionObserver(entries => {\n      if (destroyed) return;\n      entries.forEach(entry => {\n        const index = slides.indexOf(entry.target);\n        intersectionEntryMap[index] = entry;\n      });\n      inViewCache = null;\n      notInViewCache = null;\n      eventHandler.emit('slidesInView');\n    }, {\n      root: container.parentElement,\n      threshold\n    });\n    slides.forEach(slide => intersectionObserver.observe(slide));\n  }\n  function destroy() {\n    if (intersectionObserver) intersectionObserver.disconnect();\n    destroyed = true;\n  }\n  function createInViewList(inView) {\n    return objectKeys(intersectionEntryMap).reduce((list, slideIndex) => {\n      const index = parseInt(slideIndex);\n      const {\n        isIntersecting\n      } = intersectionEntryMap[index];\n      const inViewMatch = inView && isIntersecting;\n      const notInViewMatch = !inView && !isIntersecting;\n      if (inViewMatch || notInViewMatch) list.push(index);\n      return list;\n    }, []);\n  }\n  function get(inView = true) {\n    if (inView && inViewCache) return inViewCache;\n    if (!inView && notInViewCache) return notInViewCache;\n    const slideIndexes = createInViewList(inView);\n    if (inView) inViewCache = slideIndexes;\n    if (!inView) notInViewCache = slideIndexes;\n    return slideIndexes;\n  }\n  const self = {\n    init,\n    destroy,\n    get\n  };\n  return self;\n}\n\nfunction SlideSizes(axis, containerRect, slideRects, slides, readEdgeGap, ownerWindow) {\n  const {\n    measureSize,\n    startEdge,\n    endEdge\n  } = axis;\n  const withEdgeGap = slideRects[0] && readEdgeGap;\n  const startGap = measureStartGap();\n  const endGap = measureEndGap();\n  const slideSizes = slideRects.map(measureSize);\n  const slideSizesWithGaps = measureWithGaps();\n  function measureStartGap() {\n    if (!withEdgeGap) return 0;\n    const slideRect = slideRects[0];\n    return mathAbs(containerRect[startEdge] - slideRect[startEdge]);\n  }\n  function measureEndGap() {\n    if (!withEdgeGap) return 0;\n    const style = ownerWindow.getComputedStyle(arrayLast(slides));\n    return parseFloat(style.getPropertyValue(`margin-${endEdge}`));\n  }\n  function measureWithGaps() {\n    return slideRects.map((rect, index, rects) => {\n      const isFirst = !index;\n      const isLast = index === arrayLastIndex(rects);\n      if (isFirst) return slideSizes[index] + startGap;\n      if (isLast) return slideSizes[index] + endGap;\n      return rects[index + 1][startEdge] - rect[startEdge];\n    }).map(mathAbs);\n  }\n  const self = {\n    slideSizes,\n    slideSizesWithGaps,\n    startGap,\n    endGap\n  };\n  return self;\n}\n\nfunction SlidesToScroll(axis, direction, viewSize, slidesToScroll, loop, containerRect, slideRects, startGap, endGap) {\n  const {\n    startEdge,\n    endEdge\n  } = axis;\n  const groupByNumber = isNumber(slidesToScroll);\n  function byNumber(array, groupSize) {\n    return arrayKeys(array).filter(i => i % groupSize === 0).map(i => array.slice(i, i + groupSize));\n  }\n  function bySize(array) {\n    if (!array.length) return [];\n    return arrayKeys(array).reduce((groups, rectB) => {\n      const rectA = arrayLast(groups) || 0;\n      const isFirst = rectA === 0;\n      const isLast = rectB === arrayLastIndex(array);\n      const edgeA = containerRect[startEdge] - slideRects[rectA][startEdge];\n      const edgeB = containerRect[startEdge] - slideRects[rectB][endEdge];\n      const gapA = !loop && isFirst ? direction.apply(startGap) : 0;\n      const gapB = !loop && isLast ? direction.apply(endGap) : 0;\n      const chunkSize = mathAbs(edgeB - gapB - (edgeA + gapA));\n      if (chunkSize > viewSize) groups.push(rectB);\n      if (isLast) groups.push(array.length);\n      return groups;\n    }, []).map((currentSize, index, groups) => {\n      const previousSize = Math.max(groups[index - 1] || 0);\n      return array.slice(previousSize, currentSize);\n    });\n  }\n  function groupSlides(array) {\n    return groupByNumber ? byNumber(array, slidesToScroll) : bySize(array);\n  }\n  const self = {\n    groupSlides\n  };\n  return self;\n}\n\nfunction Engine(root, container, slides, ownerDocument, ownerWindow, options, eventHandler, animations) {\n  // Options\n  const {\n    align,\n    axis: scrollAxis,\n    direction: contentDirection,\n    startIndex,\n    loop,\n    duration,\n    dragFree,\n    dragThreshold,\n    inViewThreshold,\n    slidesToScroll: groupSlides,\n    skipSnaps,\n    containScroll,\n    watchResize,\n    watchSlides,\n    watchDrag\n  } = options;\n  // Measurements\n  const containerRect = container.getBoundingClientRect();\n  const slideRects = slides.map(slide => slide.getBoundingClientRect());\n  const direction = Direction(contentDirection);\n  const axis = Axis(scrollAxis, contentDirection);\n  const viewSize = axis.measureSize(containerRect);\n  const percentOfView = PercentOfView(viewSize);\n  const alignment = Alignment(align, viewSize);\n  const containSnaps = !loop && !!containScroll;\n  const readEdgeGap = loop || !!containScroll;\n  const {\n    slideSizes,\n    slideSizesWithGaps,\n    startGap,\n    endGap\n  } = SlideSizes(axis, containerRect, slideRects, slides, readEdgeGap, ownerWindow);\n  const slidesToScroll = SlidesToScroll(axis, direction, viewSize, groupSlides, loop, containerRect, slideRects, startGap, endGap);\n  const {\n    snaps,\n    snapsAligned\n  } = ScrollSnaps(axis, alignment, containerRect, slideRects, slidesToScroll);\n  const contentSize = -arrayLast(snaps) + arrayLast(slideSizesWithGaps);\n  const {\n    snapsContained,\n    scrollContainLimit\n  } = ScrollContain(viewSize, contentSize, snapsAligned, containScroll);\n  const scrollSnaps = containSnaps ? snapsContained : snapsAligned;\n  const {\n    limit\n  } = ScrollLimit(contentSize, scrollSnaps, loop);\n  // Indexes\n  const index = Counter(arrayLastIndex(scrollSnaps), startIndex, loop);\n  const indexPrevious = index.clone();\n  const slideIndexes = arrayKeys(slides);\n  // Animation\n  const update = ({\n    dragHandler,\n    scrollBody,\n    scrollBounds,\n    eventHandler,\n    animation,\n    options: {\n      loop\n    }\n  }) => {\n    const pointerDown = dragHandler.pointerDown();\n    if (!loop) scrollBounds.constrain(pointerDown);\n    const hasSettled = scrollBody.seek().settled();\n    if (hasSettled && !pointerDown) {\n      animation.stop();\n      eventHandler.emit('settle');\n    }\n    if (!hasSettled) eventHandler.emit('scroll');\n  };\n  const render = ({\n    scrollBody,\n    translate,\n    location,\n    offsetLocation,\n    scrollLooper,\n    slideLooper,\n    options: {\n      loop\n    }\n  }, lagOffset) => {\n    const velocity = scrollBody.velocity();\n    offsetLocation.set(location.get() - velocity + velocity * lagOffset);\n    if (loop) {\n      scrollLooper.loop(scrollBody.direction());\n      slideLooper.loop();\n    }\n    translate.to(offsetLocation.get());\n  };\n  const animation = {\n    start: () => animations.start(engine),\n    stop: () => animations.stop(engine),\n    update: () => update(engine),\n    render: lagOffset => render(engine, lagOffset)\n  };\n  // Shared\n  const friction = 0.68;\n  const startLocation = scrollSnaps[index.get()];\n  const location = Vector1D(startLocation);\n  const offsetLocation = Vector1D(startLocation);\n  const target = Vector1D(startLocation);\n  const scrollBody = ScrollBody(location, target, duration, friction);\n  const scrollTarget = ScrollTarget(loop, scrollSnaps, contentSize, limit, target);\n  const scrollTo = ScrollTo(animation, index, indexPrevious, scrollTarget, scrollBody, target, eventHandler);\n  const scrollProgress = ScrollProgress(limit);\n  const eventStore = EventStore();\n  const slidesInView = SlidesInView(container, slides, eventHandler, inViewThreshold);\n  const {\n    slideRegistry\n  } = SlideRegistry(viewSize, contentSize, containSnaps, scrollContainLimit, slidesToScroll, slideIndexes);\n  const slideFocus = SlideFocus(root, slides, slideRegistry, scrollTo, scrollBody, eventStore);\n  // Engine\n  const engine = {\n    ownerDocument,\n    ownerWindow,\n    eventHandler,\n    containerRect,\n    slideRects,\n    animation,\n    axis,\n    direction,\n    dragHandler: DragHandler(axis, direction, root, ownerDocument, ownerWindow, target, DragTracker(axis, ownerWindow), location, animation, scrollTo, scrollBody, scrollTarget, index, eventHandler, percentOfView, dragFree, dragThreshold, skipSnaps, friction, watchDrag),\n    eventStore,\n    percentOfView,\n    index,\n    indexPrevious,\n    limit,\n    location,\n    offsetLocation,\n    options,\n    resizeHandler: ResizeHandler(container, eventHandler, ownerWindow, slides, axis, watchResize),\n    scrollBody,\n    scrollBounds: ScrollBounds(limit, location, target, scrollBody, percentOfView),\n    scrollLooper: ScrollLooper(contentSize, limit, offsetLocation, [location, offsetLocation, target]),\n    scrollProgress,\n    scrollSnapList: scrollSnaps.map(scrollProgress.get),\n    scrollSnaps,\n    scrollTarget,\n    scrollTo,\n    slideLooper: SlideLooper(axis, direction, viewSize, contentSize, slideSizes, slideSizesWithGaps, snaps, scrollSnaps, offsetLocation, slides),\n    slideFocus,\n    slidesHandler: SlidesHandler(container, eventHandler, watchSlides),\n    slidesInView,\n    slideIndexes,\n    slideRegistry,\n    slidesToScroll,\n    target,\n    translate: Translate(axis, direction, container)\n  };\n  return engine;\n}\n\nfunction Animations(ownerWindow) {\n  const timeStep = 1000 / 60;\n  let engines = [];\n  let lastTimeStamp = null;\n  let lag = 0;\n  let animationFrame = 0;\n  function animate(timeStamp) {\n    if (!lastTimeStamp) lastTimeStamp = timeStamp;\n    const elapsed = timeStamp - lastTimeStamp;\n    lastTimeStamp = timeStamp;\n    lag += elapsed;\n    while (lag >= timeStep) {\n      engines.forEach(({\n        animation\n      }) => animation.update());\n      lag -= timeStep;\n    }\n    const lagOffset = mathAbs(lag / timeStep);\n    engines.forEach(({\n      animation\n    }) => animation.render(lagOffset));\n    if (animationFrame) ownerWindow.requestAnimationFrame(animate);\n  }\n  function start(engine) {\n    if (!engines.includes(engine)) engines.push(engine);\n    if (animationFrame) return;\n    animationFrame = ownerWindow.requestAnimationFrame(animate);\n  }\n  function stop(engine) {\n    engines = engines.filter(e => e !== engine);\n    if (engines.length) return;\n    ownerWindow.cancelAnimationFrame(animationFrame);\n    lastTimeStamp = null;\n    lag = 0;\n    animationFrame = 0;\n  }\n  function reset() {\n    lastTimeStamp = null;\n    lag = 0;\n  }\n  const self = {\n    start,\n    stop,\n    reset,\n    window: ownerWindow\n  };\n  return self;\n}\n\nfunction EventHandler() {\n  const listeners = {};\n  let api;\n  function init(emblaApi) {\n    api = emblaApi;\n  }\n  function getListeners(evt) {\n    return listeners[evt] || [];\n  }\n  function emit(evt) {\n    getListeners(evt).forEach(e => e(api, evt));\n    return self;\n  }\n  function on(evt, cb) {\n    listeners[evt] = getListeners(evt).concat([cb]);\n    return self;\n  }\n  function off(evt, cb) {\n    listeners[evt] = getListeners(evt).filter(e => e !== cb);\n    return self;\n  }\n  const self = {\n    init,\n    emit,\n    off,\n    on\n  };\n  return self;\n}\n\nconst defaultOptions = {\n  align: 'center',\n  axis: 'x',\n  container: null,\n  slides: null,\n  containScroll: 'trimSnaps',\n  direction: 'ltr',\n  slidesToScroll: 1,\n  inViewThreshold: 0,\n  breakpoints: {},\n  dragFree: false,\n  dragThreshold: 10,\n  loop: false,\n  skipSnaps: false,\n  duration: 25,\n  startIndex: 0,\n  active: true,\n  watchDrag: true,\n  watchResize: true,\n  watchSlides: true\n};\n\nfunction OptionsHandler(ownerWindow) {\n  function mergeOptions(optionsA, optionsB) {\n    return objectsMergeDeep(optionsA, optionsB || {});\n  }\n  function optionsAtMedia(options) {\n    const optionsAtMedia = options.breakpoints || {};\n    const matchedMediaOptions = objectKeys(optionsAtMedia).filter(media => ownerWindow.matchMedia(media).matches).map(media => optionsAtMedia[media]).reduce((a, mediaOption) => mergeOptions(a, mediaOption), {});\n    return mergeOptions(options, matchedMediaOptions);\n  }\n  function optionsMediaQueries(optionsList) {\n    return optionsList.map(options => objectKeys(options.breakpoints || {})).reduce((acc, mediaQueries) => acc.concat(mediaQueries), []).map(ownerWindow.matchMedia);\n  }\n  const self = {\n    mergeOptions,\n    optionsAtMedia,\n    optionsMediaQueries\n  };\n  return self;\n}\n\nfunction PluginsHandler(optionsHandler) {\n  let activePlugins = [];\n  function init(emblaApi, plugins) {\n    activePlugins = plugins.filter(({\n      options\n    }) => optionsHandler.optionsAtMedia(options).active !== false);\n    activePlugins.forEach(plugin => plugin.init(emblaApi, optionsHandler));\n    return plugins.reduce((map, plugin) => Object.assign(map, {\n      [plugin.name]: plugin\n    }), {});\n  }\n  function destroy() {\n    activePlugins = activePlugins.filter(plugin => plugin.destroy());\n  }\n  const self = {\n    init,\n    destroy\n  };\n  return self;\n}\n\nfunction EmblaCarousel(root, userOptions, userPlugins) {\n  const ownerDocument = root.ownerDocument;\n  const ownerWindow = ownerDocument.defaultView;\n  const optionsHandler = OptionsHandler(ownerWindow);\n  const pluginsHandler = PluginsHandler(optionsHandler);\n  const mediaHandlers = EventStore();\n  const documentVisibleHandler = EventStore();\n  const eventHandler = EventHandler();\n  const {\n    animationRealms\n  } = EmblaCarousel;\n  const {\n    mergeOptions,\n    optionsAtMedia,\n    optionsMediaQueries\n  } = optionsHandler;\n  const {\n    on,\n    off,\n    emit\n  } = eventHandler;\n  const reInit = reActivate;\n  let destroyed = false;\n  let engine;\n  let optionsBase = mergeOptions(defaultOptions, EmblaCarousel.globalOptions);\n  let options = mergeOptions(optionsBase);\n  let pluginList = [];\n  let pluginApis;\n  let container;\n  let slides;\n  function storeElements() {\n    const {\n      container: userContainer,\n      slides: userSlides\n    } = options;\n    const customContainer = isString(userContainer) ? root.querySelector(userContainer) : userContainer;\n    container = customContainer || root.children[0];\n    const customSlides = isString(userSlides) ? container.querySelectorAll(userSlides) : userSlides;\n    slides = [].slice.call(customSlides || container.children);\n  }\n  function createEngine(options, animations) {\n    const engine = Engine(root, container, slides, ownerDocument, ownerWindow, options, eventHandler, animations);\n    if (options.loop && !engine.slideLooper.canLoop()) {\n      const optionsWithoutLoop = Object.assign({}, options, {\n        loop: false\n      });\n      return createEngine(optionsWithoutLoop, animations);\n    }\n    return engine;\n  }\n  function activate(withOptions, withPlugins) {\n    if (destroyed) return;\n    const animationRealm = animationRealms.find(a => a.window === ownerWindow);\n    const animations = animationRealm || Animations(ownerWindow);\n    if (!animationRealm) animationRealms.push(animations);\n    optionsBase = mergeOptions(optionsBase, withOptions);\n    options = optionsAtMedia(optionsBase);\n    pluginList = withPlugins || pluginList;\n    storeElements();\n    engine = createEngine(options, animations);\n    optionsMediaQueries([optionsBase, ...pluginList.map(({\n      options\n    }) => options)]).forEach(query => mediaHandlers.add(query, 'change', reActivate));\n    if (!options.active) return;\n    engine.translate.to(engine.location.get());\n    engine.slidesInView.init();\n    engine.slideFocus.init();\n    engine.eventHandler.init(self);\n    engine.resizeHandler.init(self);\n    engine.slidesHandler.init(self);\n    documentVisibleHandler.add(ownerDocument, 'visibilitychange', () => {\n      if (ownerDocument.hidden) animations.reset();\n    });\n    if (engine.options.loop) engine.slideLooper.loop();\n    if (container.offsetParent && slides.length) engine.dragHandler.init(self);\n    pluginApis = pluginsHandler.init(self, pluginList);\n  }\n  function reActivate(withOptions, withPlugins) {\n    const startIndex = selectedScrollSnap();\n    deActivate();\n    activate(mergeOptions({\n      startIndex\n    }, withOptions), withPlugins);\n    eventHandler.emit('reInit');\n  }\n  function deActivate() {\n    engine.dragHandler.destroy();\n    engine.animation.stop();\n    engine.eventStore.clear();\n    engine.translate.clear();\n    engine.slideLooper.clear();\n    engine.resizeHandler.destroy();\n    engine.slidesHandler.destroy();\n    pluginsHandler.destroy();\n    mediaHandlers.clear();\n    documentVisibleHandler.clear();\n  }\n  function destroy() {\n    if (destroyed) return;\n    destroyed = true;\n    mediaHandlers.clear();\n    deActivate();\n    eventHandler.emit('destroy');\n  }\n  function scrollTo(index, jump, direction) {\n    if (!options.active || destroyed) return;\n    engine.scrollBody.useBaseFriction().useDuration(jump ? 0 : options.duration);\n    engine.scrollTo.index(index, direction || 0);\n  }\n  function scrollNext(jump) {\n    const next = engine.index.add(1).get();\n    scrollTo(next, jump === true, -1);\n  }\n  function scrollPrev(jump) {\n    const prev = engine.index.add(-1).get();\n    scrollTo(prev, jump === true, 1);\n  }\n  function canScrollNext() {\n    const next = engine.index.add(1).get();\n    return next !== selectedScrollSnap();\n  }\n  function canScrollPrev() {\n    const prev = engine.index.add(-1).get();\n    return prev !== selectedScrollSnap();\n  }\n  function scrollSnapList() {\n    return engine.scrollSnapList;\n  }\n  function scrollProgress() {\n    return engine.scrollProgress.get(engine.location.get());\n  }\n  function selectedScrollSnap() {\n    return engine.index.get();\n  }\n  function previousScrollSnap() {\n    return engine.indexPrevious.get();\n  }\n  function slidesInView() {\n    return engine.slidesInView.get();\n  }\n  function slidesNotInView() {\n    return engine.slidesInView.get(false);\n  }\n  function plugins() {\n    return pluginApis;\n  }\n  function internalEngine() {\n    return engine;\n  }\n  function rootNode() {\n    return root;\n  }\n  function containerNode() {\n    return container;\n  }\n  function slideNodes() {\n    return slides;\n  }\n  const self = {\n    canScrollNext,\n    canScrollPrev,\n    containerNode,\n    internalEngine,\n    destroy,\n    off,\n    on,\n    emit,\n    plugins,\n    previousScrollSnap,\n    reInit,\n    rootNode,\n    scrollNext,\n    scrollPrev,\n    scrollProgress,\n    scrollSnapList,\n    scrollTo,\n    selectedScrollSnap,\n    slideNodes,\n    slidesInView,\n    slidesNotInView\n  };\n  activate(userOptions, userPlugins);\n  setTimeout(() => eventHandler.emit('init'), 0);\n  return self;\n}\nEmblaCarousel.animationRealms = [];\nEmblaCarousel.globalOptions = undefined;\n\n\n//# sourceMappingURL=embla-carousel.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/embla-carousel/embla-carousel.esm.js\n");

/***/ })

};
;