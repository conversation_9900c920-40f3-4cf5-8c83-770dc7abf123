"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/motion-dom";
exports.ids = ["vendor-chunks/motion-dom"];
exports.modules = {

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isDragActive: () => (/* binding */ isDragActive),\n/* harmony export */   isDragging: () => (/* binding */ isDragging)\n/* harmony export */ });\nconst isDragging = {\n    x: false,\n    y: false,\n};\nfunction isDragActive() {\n    return isDragging.x || isDragging.y;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL2RyYWcvc3RhdGUvaXMtYWN0aXZlLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVvQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyb3hhc1xcT25lRHJpdmVcXERlc2t0b3BcXFBST0pFQ1RTXFx0dWNzb25sb3Zlc211c2ljLmNvbVxcUHJvdG90eXBlMTNcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxnZXN0dXJlc1xcZHJhZ1xcc3RhdGVcXGlzLWFjdGl2ZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgaXNEcmFnZ2luZyA9IHtcbiAgICB4OiBmYWxzZSxcbiAgICB5OiBmYWxzZSxcbn07XG5mdW5jdGlvbiBpc0RyYWdBY3RpdmUoKSB7XG4gICAgcmV0dXJuIGlzRHJhZ2dpbmcueCB8fCBpc0RyYWdnaW5nLnk7XG59XG5cbmV4cG9ydCB7IGlzRHJhZ0FjdGl2ZSwgaXNEcmFnZ2luZyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setDragLock: () => (/* binding */ setDragLock)\n/* harmony export */ });\n/* harmony import */ var _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is-active.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n\n\nfunction setDragLock(axis) {\n    if (axis === \"x\" || axis === \"y\") {\n        if (_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis]) {\n            return null;\n        }\n        else {\n            _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis] = true;\n            return () => {\n                _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis] = false;\n            };\n        }\n    }\n    else {\n        if (_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x || _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y) {\n            return null;\n        }\n        else {\n            _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x = _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y = true;\n            return () => {\n                _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x = _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y = false;\n            };\n        }\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL2RyYWcvc3RhdGUvc2V0LWFjdGl2ZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNkM7O0FBRTdDO0FBQ0E7QUFDQSxZQUFZLHNEQUFVO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBLFlBQVksc0RBQVU7QUFDdEI7QUFDQSxnQkFBZ0Isc0RBQVU7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHNEQUFVLE1BQU0sc0RBQVU7QUFDdEM7QUFDQTtBQUNBO0FBQ0EsWUFBWSxzREFBVSxLQUFLLHNEQUFVO0FBQ3JDO0FBQ0EsZ0JBQWdCLHNEQUFVLEtBQUssc0RBQVU7QUFDekM7QUFDQTtBQUNBO0FBQ0E7O0FBRXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJveGFzXFxPbmVEcml2ZVxcRGVza3RvcFxcUFJPSkVDVFNcXHR1Y3NvbmxvdmVzbXVzaWMuY29tXFxQcm90b3R5cGUxM1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGdlc3R1cmVzXFxkcmFnXFxzdGF0ZVxcc2V0LWFjdGl2ZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNEcmFnZ2luZyB9IGZyb20gJy4vaXMtYWN0aXZlLm1qcyc7XG5cbmZ1bmN0aW9uIHNldERyYWdMb2NrKGF4aXMpIHtcbiAgICBpZiAoYXhpcyA9PT0gXCJ4XCIgfHwgYXhpcyA9PT0gXCJ5XCIpIHtcbiAgICAgICAgaWYgKGlzRHJhZ2dpbmdbYXhpc10pIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgaXNEcmFnZ2luZ1theGlzXSA9IHRydWU7XG4gICAgICAgICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICAgICAgICAgIGlzRHJhZ2dpbmdbYXhpc10gPSBmYWxzZTtcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIGlmIChpc0RyYWdnaW5nLnggfHwgaXNEcmFnZ2luZy55KSB7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGlzRHJhZ2dpbmcueCA9IGlzRHJhZ2dpbmcueSA9IHRydWU7XG4gICAgICAgICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICAgICAgICAgIGlzRHJhZ2dpbmcueCA9IGlzRHJhZ2dpbmcueSA9IGZhbHNlO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH1cbn1cblxuZXhwb3J0IHsgc2V0RHJhZ0xvY2sgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/hover.mjs":
/*!************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/hover.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hover: () => (/* binding */ hover)\n/* harmony export */ });\n/* harmony import */ var _utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/resolve-elements.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\");\n/* harmony import */ var _drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./drag/state/is-active.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n\n\n\n/**\n * Filter out events that are not pointer events, or are triggering\n * while a Motion gesture is active.\n */\nfunction filterEvents(callback) {\n    return (event) => {\n        if (event.pointerType === \"touch\" || (0,_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_1__.isDragActive)())\n            return;\n        callback(event);\n    };\n}\n/**\n * Create a hover gesture. hover() is different to .addEventListener(\"pointerenter\")\n * in that it has an easier syntax, filters out polyfilled touch events, interoperates\n * with drag gestures, and automatically removes the \"pointerennd\" event listener when the hover ends.\n *\n * @public\n */\nfunction hover(elementOrSelector, onHoverStart, options = {}) {\n    const gestureAbortController = new AbortController();\n    const eventOptions = {\n        passive: true,\n        ...options,\n        signal: gestureAbortController.signal,\n    };\n    const onPointerEnter = filterEvents((enterEvent) => {\n        const { target } = enterEvent;\n        const onHoverEnd = onHoverStart(enterEvent);\n        if (!onHoverEnd || !target)\n            return;\n        const onPointerLeave = filterEvents((leaveEvent) => {\n            onHoverEnd(leaveEvent);\n            target.removeEventListener(\"pointerleave\", onPointerLeave);\n        });\n        target.addEventListener(\"pointerleave\", onPointerLeave, eventOptions);\n    });\n    (0,_utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveElements)(elementOrSelector).forEach((element) => {\n        element.addEventListener(\"pointerenter\", onPointerEnter, eventOptions);\n    });\n    return () => gestureAbortController.abort();\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/hover.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/index.mjs":
/*!***************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/index.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hover: () => (/* reexport safe */ _gestures_hover_mjs__WEBPACK_IMPORTED_MODULE_0__.hover),\n/* harmony export */   isDragActive: () => (/* reexport safe */ _gestures_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_2__.isDragActive),\n/* harmony export */   isDragging: () => (/* reexport safe */ _gestures_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_2__.isDragging),\n/* harmony export */   resolveElements: () => (/* reexport safe */ _utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_1__.resolveElements),\n/* harmony export */   setDragLock: () => (/* reexport safe */ _gestures_drag_state_set_active_mjs__WEBPACK_IMPORTED_MODULE_3__.setDragLock)\n/* harmony export */ });\n/* harmony import */ var _gestures_hover_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./gestures/hover.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/hover.mjs\");\n/* harmony import */ var _utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/resolve-elements.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\");\n/* harmony import */ var _gestures_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./gestures/drag/state/is-active.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n/* harmony import */ var _gestures_drag_state_set_active_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./gestures/drag/state/set-active.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBNkM7QUFDa0I7QUFDZ0I7QUFDWiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyb3hhc1xcT25lRHJpdmVcXERlc2t0b3BcXFBST0pFQ1RTXFx0dWNzb25sb3Zlc211c2ljLmNvbVxcUHJvdG90eXBlMTNcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgaG92ZXIgfSBmcm9tICcuL2dlc3R1cmVzL2hvdmVyLm1qcyc7XG5leHBvcnQgeyByZXNvbHZlRWxlbWVudHMgfSBmcm9tICcuL3V0aWxzL3Jlc29sdmUtZWxlbWVudHMubWpzJztcbmV4cG9ydCB7IGlzRHJhZ0FjdGl2ZSwgaXNEcmFnZ2luZyB9IGZyb20gJy4vZ2VzdHVyZXMvZHJhZy9zdGF0ZS9pcy1hY3RpdmUubWpzJztcbmV4cG9ydCB7IHNldERyYWdMb2NrIH0gZnJvbSAnLi9nZXN0dXJlcy9kcmFnL3N0YXRlL3NldC1hY3RpdmUubWpzJztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveElements: () => (/* binding */ resolveElements)\n/* harmony export */ });\nfunction resolveElements(elementOrSelector, scope, selectorCache) {\n    var _a;\n    if (elementOrSelector instanceof Element) {\n        return [elementOrSelector];\n    }\n    else if (typeof elementOrSelector === \"string\") {\n        let root = document;\n        if (scope) {\n            // TODO: Refactor to utils package\n            // invariant(\n            //     Boolean(scope.current),\n            //     \"Scope provided, but no element detected.\"\n            // )\n            root = scope.current;\n        }\n        const elements = (_a = selectorCache === null || selectorCache === void 0 ? void 0 : selectorCache[elementOrSelector]) !== null && _a !== void 0 ? _a : root.querySelectorAll(elementOrSelector);\n        return elements ? Array.from(elements) : [];\n    }\n    return Array.from(elementOrSelector);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL3Jlc29sdmUtZWxlbWVudHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUUyQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyb3hhc1xcT25lRHJpdmVcXERlc2t0b3BcXFBST0pFQ1RTXFx0dWNzb25sb3Zlc211c2ljLmNvbVxcUHJvdG90eXBlMTNcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFx1dGlsc1xccmVzb2x2ZS1lbGVtZW50cy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gcmVzb2x2ZUVsZW1lbnRzKGVsZW1lbnRPclNlbGVjdG9yLCBzY29wZSwgc2VsZWN0b3JDYWNoZSkge1xuICAgIHZhciBfYTtcbiAgICBpZiAoZWxlbWVudE9yU2VsZWN0b3IgaW5zdGFuY2VvZiBFbGVtZW50KSB7XG4gICAgICAgIHJldHVybiBbZWxlbWVudE9yU2VsZWN0b3JdO1xuICAgIH1cbiAgICBlbHNlIGlmICh0eXBlb2YgZWxlbWVudE9yU2VsZWN0b3IgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgbGV0IHJvb3QgPSBkb2N1bWVudDtcbiAgICAgICAgaWYgKHNjb3BlKSB7XG4gICAgICAgICAgICAvLyBUT0RPOiBSZWZhY3RvciB0byB1dGlscyBwYWNrYWdlXG4gICAgICAgICAgICAvLyBpbnZhcmlhbnQoXG4gICAgICAgICAgICAvLyAgICAgQm9vbGVhbihzY29wZS5jdXJyZW50KSxcbiAgICAgICAgICAgIC8vICAgICBcIlNjb3BlIHByb3ZpZGVkLCBidXQgbm8gZWxlbWVudCBkZXRlY3RlZC5cIlxuICAgICAgICAgICAgLy8gKVxuICAgICAgICAgICAgcm9vdCA9IHNjb3BlLmN1cnJlbnQ7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgZWxlbWVudHMgPSAoX2EgPSBzZWxlY3RvckNhY2hlID09PSBudWxsIHx8IHNlbGVjdG9yQ2FjaGUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHNlbGVjdG9yQ2FjaGVbZWxlbWVudE9yU2VsZWN0b3JdKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiByb290LnF1ZXJ5U2VsZWN0b3JBbGwoZWxlbWVudE9yU2VsZWN0b3IpO1xuICAgICAgICByZXR1cm4gZWxlbWVudHMgPyBBcnJheS5mcm9tKGVsZW1lbnRzKSA6IFtdO1xuICAgIH1cbiAgICByZXR1cm4gQXJyYXkuZnJvbShlbGVtZW50T3JTZWxlY3Rvcik7XG59XG5cbmV4cG9ydCB7IHJlc29sdmVFbGVtZW50cyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\n");

/***/ })

};
;