"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_components_map_event-map_tsx"],{

/***/ "(app-pages-browser)/./components/map/event-map-wrapper.tsx":
/*!**********************************************!*\
  !*** ./components/map/event-map-wrapper.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EventMap)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Dynamically import the manual map component with no SSR\nconst DynamicEventMap = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_map_event-map-manual_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./event-map-manual */ \"(app-pages-browser)/./components/map/event-map-manual.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\map\\\\event-map-wrapper.tsx -> \" + \"./event-map-manual\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full w-full flex items-center justify-center\",\n            style: {\n                minHeight: '400px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-muted-foreground\",\n                children: \"Loading map...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\map\\\\event-map-wrapper.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\map\\\\event-map-wrapper.tsx\",\n            lineNumber: 23,\n            columnNumber: 5\n        }, undefined)\n});\n_c = DynamicEventMap;\nfunction EventMap(props) {\n    _s();\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mapKey, setMapKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Ensure we're only rendering on the client\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventMap.useEffect\": ()=>{\n            setIsClient(true);\n            // Force a new key to ensure fresh component mount\n            setMapKey(Date.now());\n        }\n    }[\"EventMap.useEffect\"], []);\n    // Reset the map when center changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventMap.useEffect\": ()=>{\n            if (props.center) {\n                setMapKey(Date.now());\n            }\n        }\n    }[\"EventMap.useEffect\"], [\n        props.center\n    ]);\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full w-full flex items-center justify-center\",\n            style: {\n                minHeight: '400px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-muted-foreground\",\n                children: \"Loading map...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\map\\\\event-map-wrapper.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\map\\\\event-map-wrapper.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DynamicEventMap, {\n        ...props\n    }, mapKey, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\map\\\\event-map-wrapper.tsx\",\n        lineNumber: 55,\n        columnNumber: 10\n    }, this);\n}\n_s(EventMap, \"uVd2yMxtP1sXmBET+0nKhGPrG28=\");\n_c1 = EventMap;\nvar _c, _c1;\n$RefreshReg$(_c, \"DynamicEventMap\");\n$RefreshReg$(_c1, \"EventMap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/map/event-map-wrapper.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/map/event-map.tsx":
/*!**************************************!*\
  !*** ./components/map/event-map.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _event_map_wrapper__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _event_map_wrapper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./event-map-wrapper */ \"(app-pages-browser)/./components/map/event-map-wrapper.tsx\");\n// Re-export the wrapper component\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvbWFwL2V2ZW50LW1hcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxrQ0FBa0M7QUFDWSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyb3hhc1xcT25lRHJpdmVcXERlc2t0b3BcXFBST0pFQ1RTXFx0dWNzb25sb3Zlc211c2ljLmNvbVxcUHJvdG90eXBlMTNcXGZyb250ZW5kXFxjb21wb25lbnRzXFxtYXBcXGV2ZW50LW1hcC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiLy8gUmUtZXhwb3J0IHRoZSB3cmFwcGVyIGNvbXBvbmVudFxyXG5leHBvcnQgeyBkZWZhdWx0IH0gZnJvbSAnLi9ldmVudC1tYXAtd3JhcHBlcic7Il0sIm5hbWVzIjpbImRlZmF1bHQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/map/event-map.tsx\n"));

/***/ })

}]);