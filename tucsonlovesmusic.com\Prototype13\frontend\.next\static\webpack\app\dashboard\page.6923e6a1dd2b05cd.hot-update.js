"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/admin/event-relations/talent.tsx":
/*!*****************************************************!*\
  !*** ./components/admin/event-relations/talent.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventTalentRelations: () => (/* binding */ EventTalentRelations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,FileText,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/git-merge.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,FileText,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,FileText,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,FileText,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,FileText,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,FileText,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,FileText,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,FileText,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,ChevronDown,ChevronRight,FileText,GitMerge,Loader2,RefreshCw,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-auth */ \"(app-pages-browser)/./hooks/use-auth.ts\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./components/ui/collapsible.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/config */ \"(app-pages-browser)/./lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ EventTalentRelations auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction EventTalentRelations() {\n    _s();\n    const [counts, setCounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [missingEvents, setMissingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mismatchedEvents, setMismatchedEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isBulkFixing, setIsBulkFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [batchFixing, setBatchFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [csvFixing, setCsvFixing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Add this line\n    const [batchFixProgress, setBatchFixProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        current: 0,\n        total: 0,\n        success: 0,\n        failed: 0\n    });\n    const [fixingEvents, setFixingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}); // Track which events are being fixed\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        missing: false,\n        mismatched: false\n    });\n    const [confirmDialogOpen, setConfirmDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { getAccessToken } = (0,_hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    // Handle CSV-based fix for all event-talent relationships\n    const handleCsvFix = async ()=>{\n        try {\n            setCsvFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            console.log('🔧 [Frontend] Starting CSV-based fix for event-talent relationships');\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations/fix-from-csv\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('🔧 [Frontend] Server error response:', errorText);\n                throw new Error(\"Failed to execute CSV fix: \".concat(response.status, \" \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log('🔧 [Frontend] CSV fix completed successfully:', result);\n            toast({\n                title: 'CSV Fix Complete',\n                description: result.message || 'Successfully processed event-talent relationships from CSV.',\n                variant: 'default'\n            });\n            // Refresh data to show updated counts\n            fetchData();\n        } catch (err) {\n            console.error('🔧 [Frontend] Error during CSV fix operation', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to complete CSV fix operation',\n                variant: 'destructive'\n            });\n        } finally{\n            setCsvFixing(false);\n        }\n    };\n    // Fetch data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventTalentRelations.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"EventTalentRelations.useEffect\"], []);\n    const fetchData = async ()=>{\n        try {\n            setIsLoading(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Fetch counts (2025+ events only)\n            const countsResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations?year=2025\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (!countsResponse.ok) {\n                throw new Error('Failed to fetch event-talent relationship counts');\n            }\n            const countsData = await countsResponse.json();\n            setCounts(countsData);\n            // Fetch missing events (2025+ events only)\n            const missingResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations/missing?year=2025\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (missingResponse.ok) {\n                const missingData = await missingResponse.json();\n                setMissingEvents(missingData);\n            }\n            // Fetch mismatched events (2025+ events only)\n            const mismatchedResponse = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations/mismatched?year=2025\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (mismatchedResponse.ok) {\n                const mismatchedData = await mismatchedResponse.json();\n                setMismatchedEvents(mismatchedData);\n            }\n            setError(null);\n        } catch (err) {\n            console.error('Error fetching event-talent relationship data', err);\n            setError(err instanceof Error ? err.message : 'Failed to fetch event-talent relationship data');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const toggleExpanded = (section)=>{\n        setExpanded((prev)=>({\n                ...prev,\n                [section]: !prev[section]\n            }));\n    };\n    const handleFixAll = async ()=>{\n        try {\n            setIsBulkFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations/fix-all-aggregated\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fix all event-talent relationships');\n            }\n            const result = await response.json();\n            toast({\n                title: 'Success',\n                description: result.message,\n                variant: 'default'\n            });\n            // Refresh data\n            fetchData();\n        } catch (err) {\n            console.error('Error fixing all event-talent relationships', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to fix all event-talent relationships',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsBulkFixing(false);\n        }\n    };\n    // Handle batch fixing using the bulk fix endpoint for better performance\n    const handleBatchFix = async ()=>{\n        try {\n            setBatchFixing(true);\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            // Check if there are events that need fixing\n            if (missingEvents.length === 0 && mismatchedEvents.length === 0) {\n                toast({\n                    title: 'No Events to Fix',\n                    description: 'There are no events that need fixing.',\n                    variant: 'default'\n                });\n                return;\n            }\n            console.log(\"\\uD83D\\uDD27 [Frontend] Starting bulk batch fix for all events\");\n            // Use the new aggregated batch fix endpoint which calls individual fixes for each event\n            // Pass the same year filter (2025) that's used for displaying events\n            const response = await fetch(\"\".concat(baseUrl, \"/admin/event-talent-relations/fix-all-aggregated?year=2025\"), {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to execute batch fix: \".concat(response.status, \" \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log(\"\\uD83D\\uDD27 [Frontend] Batch fix completed successfully\");\n            toast({\n                title: 'Batch Fix Complete',\n                description: result.message || 'Successfully completed batch fix operation.',\n                variant: 'default'\n            });\n            // Refresh data to show updated counts\n            fetchData();\n        } catch (err) {\n            console.error('Error during batch fix operation', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : 'Failed to complete batch fix operation',\n                variant: 'destructive'\n            });\n        } finally{\n            setBatchFixing(false);\n        }\n    };\n    // Open confirmation dialog for fixing an event\n    const openFixConfirmDialog = (event)=>{\n        setSelectedEvent(event);\n        setConfirmDialogOpen(true);\n    };\n    // Handle fixing a single event's talent relationships\n    const handleFixSingleEvent = async ()=>{\n        if (!selectedEvent) return;\n        try {\n            // Close the dialog\n            setConfirmDialogOpen(false);\n            // Mark this event as being fixed\n            setFixingEvents((prev)=>({\n                    ...prev,\n                    [selectedEvent.id]: true\n                }));\n            console.log(\"\\uD83D\\uDD27 [Frontend] Starting fix for event: \".concat(selectedEvent.name, \" (ID: \").concat(selectedEvent.id, \")\"));\n            console.log(\"\\uD83D\\uDD27 [Frontend] Event details:\", {\n                id: selectedEvent.id,\n                name: selectedEvent.name,\n                azure_talent_ids: selectedEvent.azure_talent_ids,\n                postgres_talent_count: selectedEvent.postgres_talent_count\n            });\n            const token = await getAccessToken();\n            const baseUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_9__.getApiUrl)();\n            const url = \"\".concat(baseUrl, \"/admin/event-talent-relations/fix/\").concat(selectedEvent.id);\n            console.log(\"\\uD83D\\uDD27 [Frontend] Making POST request to: \".concat(url));\n            const response = await fetch(url, {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            console.log(\"\\uD83D\\uDD27 [Frontend] Response status: \".concat(response.status, \" \").concat(response.statusText));\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"\\uD83D\\uDD27 [Frontend] Server error response:\", errorText);\n                throw new Error(\"Failed to fix event-talent relationship for \".concat(selectedEvent.name, \": \").concat(response.status, \" \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log(\"\\uD83D\\uDD27 [Frontend] Success response:\", result);\n            toast({\n                title: 'Success',\n                description: result.message || 'Fixed musician relationships for \"'.concat(selectedEvent.name, '\"'),\n                variant: 'default'\n            });\n            console.log(\"\\uD83D\\uDD27 [Frontend] Refreshing data after successful fix\");\n            // Refresh data after successful fix\n            fetchData();\n        } catch (err) {\n            console.error('🔧 [Frontend] Error fixing event-talent relationship', err);\n            toast({\n                title: 'Error',\n                description: err instanceof Error ? err.message : \"Failed to fix event-talent relationship for \".concat(selectedEvent.name),\n                variant: 'destructive'\n            });\n        } finally{\n            // Clear the fixing state for this event\n            if (selectedEvent) {\n                setFixingEvents((prev)=>({\n                        ...prev,\n                        [selectedEvent.id]: false\n                    }));\n            }\n            // Clear the selected event\n            setSelectedEvent(null);\n            console.log(\"\\uD83D\\uDD27 [Frontend] Fix operation completed\");\n        }\n    };\n    // Determine status message and icon color\n    const getStatusDetails = ()=>{\n        if (!counts) return {\n            message: 'Loading...',\n            status: 'neutral'\n        };\n        // Check if there are any actual problematic relationships in the dropdown lists\n        const hasProblematicRelations = missingEvents.length > 0 || mismatchedEvents.length > 0;\n        // If there are no problematic relations to troubleshoot, show green\n        // Only show yellow warning when there's something that needs attention\n        if (!hasProblematicRelations) {\n            return {\n                message: counts.missingTalents === 0 ? 'All events have talent relationships' : \"\".concat(counts.missingTalents, \" events with no musician relations\"),\n                status: 'good'\n            };\n        }\n        // Show yellow warning when there are items in the dropdown lists that need attention\n        return {\n            message: \"\".concat(counts.missingTalents, \" events (2025+) with no musician relations\"),\n            status: 'warning'\n        };\n    };\n    // Get the appropriate icon color based on status\n    const getIconColorClass = (status)=>{\n        switch(status){\n            case 'good':\n                return 'text-green-500';\n            case 'warning':\n                return 'text-yellow-500';\n            case 'bad':\n                return 'text-red-500';\n            default:\n                return 'text-muted-foreground';\n        }\n    };\n    const statusDetails = getStatusDetails();\n    // Format date for display\n    const formatDate = (dateString)=>{\n        if (!dateString) return 'No date';\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"flex flex-row items-center space-y-0 pb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-5 w-5 mr-2 \".concat(getIconColorClass(statusDetails.status))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-xl\",\n                            children: \"Events - Musicians\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                lineNumber: 420,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center justify-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-8 w-8 animate-spin text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-muted-foreground\",\n                            children: \"Loading relationship data...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                    lineNumber: 429,\n                    columnNumber: 11\n                }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center py-8 px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"h-5 w-5 text-red-500 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-red-500\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: statusDetails.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleBatchFix,\n                                        disabled: batchFixing || missingEvents.length === 0,\n                                        className: \"flex items-center\",\n                                        variant: \"outline\",\n                                        children: batchFixing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Fixing \",\n                                                batchFixProgress.current,\n                                                \"/\",\n                                                batchFixProgress.total,\n                                                \"...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Batch Fix (10 Events)\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleCsvFix,\n                                        disabled: csvFixing || (counts === null || counts === void 0 ? void 0 : counts.missingTalents) === 0,\n                                        className: \"flex items-center\",\n                                        variant: \"secondary\",\n                                        children: csvFixing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Processing CSV...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Fix from CSV\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"outline\",\n                                        size: \"icon\",\n                                        onClick: fetchData,\n                                        disabled: isLoading || batchFixing || csvFixing,\n                                        title: \"Refresh data\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 15\n                            }, this),\n                            batchFixing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 p-2 bg-muted rounded-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm mb-1 flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Batch Fix Progress\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    batchFixProgress.current,\n                                                    \"/\",\n                                                    batchFixProgress.total,\n                                                    \" events\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-secondary h-2 rounded-full overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-primary h-full transition-all duration-300 ease-in-out\",\n                                            style: {\n                                                width: \"\".concat(batchFixProgress.current / Math.max(batchFixProgress.total, 1) * 100, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-xs mt-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-500\",\n                                                children: [\n                                                    batchFixProgress.success,\n                                                    \" successful\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-500\",\n                                                children: [\n                                                    batchFixProgress.failed,\n                                                    \" failed\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.Collapsible, {\n                                        open: expanded.missing,\n                                        onOpenChange: ()=>toggleExpanded('missing'),\n                                        className: \"border rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleTrigger, {\n                                                className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Events Missing Musician Relationships (\",\n                                                                    missingEvents.length,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 552,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    expanded.missing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleContent, {\n                                                className: \"px-4 pb-4\",\n                                                children: missingEvents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground py-2\",\n                                                    children: \"No events missing musician relationships\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Event Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 568,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Date\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 569,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Venue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 570,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Azure Talent IDs\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 571,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"PostgreSQL Talents\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 572,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Action\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 573,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                                            children: missingEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            className: \"font-medium\",\n                                                                            children: event.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 579,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: formatDate(event.date)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 580,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: event.venue_name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 581,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: event.azure_talent_ids.length\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 582,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            className: \"text-red-500\",\n                                                                            children: event.postgres_talent_count\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 583,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>openFixConfirmDialog(event),\n                                                                                disabled: fixingEvents[event.id],\n                                                                                children: fixingEvents[event.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                            className: \"mr-1 h-3 w-3 animate-spin\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                                            lineNumber: 593,\n                                                                                            columnNumber: 39\n                                                                                        }, this),\n                                                                                        \"Fixing...\"\n                                                                                    ]\n                                                                                }, void 0, true) : \"Fix\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                                lineNumber: 585,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 584,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, event.id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                    lineNumber: 578,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                            lineNumber: 576,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.Collapsible, {\n                                        open: expanded.mismatched,\n                                        onOpenChange: ()=>toggleExpanded('mismatched'),\n                                        className: \"border rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleTrigger, {\n                                                className: \"flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Events with Mismatched Musician Counts (\",\n                                                                    mismatchedEvents.length,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 618,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    expanded.mismatched ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleContent, {\n                                                className: \"px-4 pb-4\",\n                                                children: mismatchedEvents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground py-2\",\n                                                    children: \"No events with mismatched musician counts\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Event Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 634,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Date\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 635,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Venue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 636,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Azure Talent IDs\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 637,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"PostgreSQL Talents\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 638,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                        children: \"Action\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                        lineNumber: 639,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                                            children: mismatchedEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            className: \"font-medium\",\n                                                                            children: event.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 645,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: formatDate(event.date)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 646,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: event.venue_name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 647,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: event.azure_talent_ids.length\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 648,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            className: \"text-yellow-500\",\n                                                                            children: event.postgres_talent_count\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 649,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>openFixConfirmDialog(event),\n                                                                                disabled: fixingEvents[event.id],\n                                                                                children: fixingEvents[event.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_ChevronDown_ChevronRight_FileText_GitMerge_Loader2_RefreshCw_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                            className: \"mr-1 h-3 w-3 animate-spin\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                                            lineNumber: 659,\n                                                                                            columnNumber: 39\n                                                                                        }, this),\n                                                                                        \"Fixing...\"\n                                                                                    ]\n                                                                                }, void 0, true) : \"Fix\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                                lineNumber: 651,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                            lineNumber: 650,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, event.id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                    lineNumber: 644,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                    lineNumber: 631,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 610,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                lineNumber: 542,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                lineNumber: 427,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                open: confirmDialogOpen,\n                onOpenChange: setConfirmDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                    children: \"Fix Event-Musician Relationship\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                    lineNumber: 684,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogDescription, {\n                                    children: selectedEvent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4 py-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Are you sure you want to fix the musician relationships for this event?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 688,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted p-3 rounded-md text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Event:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 691,\n                                                                columnNumber: 24\n                                                            }, this),\n                                                            \" \",\n                                                            selectedEvent.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Date:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 692,\n                                                                columnNumber: 24\n                                                            }, this),\n                                                            \" \",\n                                                            formatDate(selectedEvent.date)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 692,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Venue:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 24\n                                                            }, this),\n                                                            \" \",\n                                                            selectedEvent.venue_name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Azure Talents:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 694,\n                                                                columnNumber: 24\n                                                            }, this),\n                                                            \" \",\n                                                            selectedEvent.azure_talent_ids.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"PostgreSQL Talents:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                                lineNumber: 695,\n                                                                columnNumber: 24\n                                                            }, this),\n                                                            \" \",\n                                                            selectedEvent.postgres_talent_count\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"This will:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 698,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-5 space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Retrieve talent relationships from Azure SQL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Update the PostgreSQL database to match\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 701,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Add missing talent relationships\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 702,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Remove incorrect talent relationships\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                        lineNumber: 687,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                    lineNumber: 685,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 683,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setConfirmDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: handleFixSingleEvent,\n                                    children: \"Fix\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                                    lineNumber: 711,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                            lineNumber: 709,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                    lineNumber: 682,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n                lineNumber: 681,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\admin\\\\event-relations\\\\talent.tsx\",\n        lineNumber: 419,\n        columnNumber: 5\n    }, this);\n}\n_s(EventTalentRelations, \"s5EYwvXrK6FOy5+KAwWcUYbv0pk=\", false, function() {\n    return [\n        _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = EventTalentRelations;\nvar _c;\n$RefreshReg$(_c, \"EventTalentRelations\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/event-relations/talent.tsx\n"));

/***/ })

});